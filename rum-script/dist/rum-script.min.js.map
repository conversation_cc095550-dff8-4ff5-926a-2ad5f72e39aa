{"version": 3, "file": "rum-script.min.js", "sources": ["../src/config.js", "../src/utils.js", "../src/errorCapture.js", "../src/errorLogger.js", "../src/userInterface.js", "../src/index.js"], "sourcesContent": ["/**\n * Default configuration for RUM Script\n */\nexport const DEFAULT_CONFIG = {\n  // Server endpoints\n  logEndpoint: '/api/errors',\n  baseUrl: 'http://localhost:3001',\n\n  // Project settings\n  projectId: 'demo_project_001',\n\n  // Error thresholds\n  errorThreshold: 5,\n  timeWindow: 5 * 60 * 1000, // 5 minutes in milliseconds\n\n  // Environment settings\n  environment: 'production',\n  enableLogging: true,\n  enableConsoleCapture: true,\n  enableNetworkCapture: true,\n  enableResourceCapture: true,\n\n  // Performance settings\n  maxErrorsPerSession: 100,\n  batchSize: 10,\n  batchTimeout: 5000, // 5 seconds\n\n  // User interface settings\n  showUserAlerts: true,\n  alertPosition: 'top-right',\n  alertDuration: 5000,\n\n  // Security settings\n  apiKey: 'demo_api_key_12345',\n  sanitizeData: true,\n  excludeUrls: [],\n  excludeMessages: [],\n\n  // Retry settings\n  maxRetries: 3,\n  retryDelay: 1000,\n\n  // Debug settings\n  debug: false,\n  verbose: false\n};\n\n/**\n * Error severity levels\n */\nexport const ERROR_SEVERITY = {\n  LOW: 'low',\n  MEDIUM: 'medium',\n  HIGH: 'high',\n  CRITICAL: 'critical'\n};\n\n/**\n * Error types\n */\nexport const ERROR_TYPES = {\n  JAVASCRIPT: 'javascript',\n  NETWORK: 'network',\n  RESOURCE: 'resource',\n  CONSOLE: 'console',\n  UNHANDLED_REJECTION: 'unhandled_rejection',\n  CUSTOM: 'custom'\n};\n\n/**\n * Alert types\n */\nexport const ALERT_TYPES = {\n  ERROR: 'error',\n  WARNING: 'warning',\n  INFO: 'info',\n  SUCCESS: 'success'\n};\n", "/**\n * Utility functions for RUM Script\n */\n\n/**\n * Generate a unique session ID\n */\nexport function generateSessionId() {\n  return 'rum_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n}\n\n/**\n * Get current timestamp in ISO format\n */\nexport function getCurrentTimestamp() {\n  return new Date().toISOString();\n}\n\n/**\n * Sanitize error data to remove sensitive information\n */\nexport function sanitizeErrorData(data, config) {\n  if (!config.sanitizeData) return data;\n\n  const sanitized = { ...data };\n\n  // Remove potential sensitive data from stack traces\n  if (sanitized.stack) {\n    sanitized.stack = sanitized.stack.replace(/\\/Users\\/<USER>\\/]+/g, '/Users/<USER>');\n    sanitized.stack = sanitized.stack.replace(/\\/home\\/<USER>\\/]+/g, '/home/<USER>');\n  }\n\n  // Remove sensitive URL parameters\n  if (sanitized.url) {\n    try {\n      const url = new URL(sanitized.url);\n      const sensitiveParams = ['token', 'key', 'password', 'secret', 'auth'];\n      sensitiveParams.forEach(param => {\n        if (url.searchParams.has(param)) {\n          url.searchParams.set(param, '***');\n        }\n      });\n      sanitized.url = url.toString();\n    } catch (e) {\n      // Invalid URL, keep as is\n    }\n  }\n\n  return sanitized;\n}\n\n/**\n * Check if error should be ignored based on configuration\n */\nexport function shouldIgnoreError(error, config) {\n  // Check excluded URLs\n  if (config.excludeUrls.length > 0) {\n    const currentUrl = window.location.href;\n    if (config.excludeUrls.some(pattern => {\n      if (typeof pattern === 'string') {\n        return currentUrl.includes(pattern);\n      }\n      if (pattern instanceof RegExp) {\n        return pattern.test(currentUrl);\n      }\n      return false;\n    })) {\n      return true;\n    }\n  }\n\n  // Check excluded messages\n  if (config.excludeMessages.length > 0 && error.message) {\n    if (config.excludeMessages.some(pattern => {\n      if (typeof pattern === 'string') {\n        return error.message.includes(pattern);\n      }\n      if (pattern instanceof RegExp) {\n        return pattern.test(error.message);\n      }\n      return false;\n    })) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Determine error severity based on error details\n */\nexport function determineErrorSeverity(error) {\n  // Import here to avoid circular dependency\n  const ERROR_SEVERITY = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n  };\n\n  // Critical errors that break functionality\n  if (error.message && (\n    error.message.includes('ChunkLoadError') ||\n    error.message.includes('Loading chunk') ||\n    error.message.includes('Cannot read property') ||\n    error.message.includes('is not a function')\n  )) {\n    return ERROR_SEVERITY.CRITICAL;\n  }\n\n  // High severity for network errors\n  if (error.type === 'network' && error.status >= 500) {\n    return ERROR_SEVERITY.HIGH;\n  }\n\n  // Medium severity for client errors\n  if (error.type === 'network' && error.status >= 400) {\n    return ERROR_SEVERITY.MEDIUM;\n  }\n\n  // Default to low severity\n  return ERROR_SEVERITY.LOW;\n}\n\n/**\n * Debounce function to limit function calls\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function () {\n    const args = arguments;\n    const context = this;\n    if (!inThrottle) {\n      func.apply(context, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Deep merge objects\n */\nexport function deepMerge(target, source) {\n  const output = Object.assign({}, target);\n  if (isObject(target) && isObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isObject(source[key])) {\n        if (!(key in target))\n          Object.assign(output, { [key]: source[key] });\n        else\n          output[key] = deepMerge(target[key], source[key]);\n      } else {\n        Object.assign(output, { [key]: source[key] });\n      }\n    });\n  }\n  return output;\n}\n\nfunction isObject(item) {\n  return item && typeof item === 'object' && !Array.isArray(item);\n}\n", "/**\n * Error capture functionality for RUM Script\n */\n\nimport { ERROR_TYPES, ERROR_SEVERITY } from './config.js';\nimport { getCurrentTimestamp, determineErrorSeverity } from './utils.js';\n\nexport class ErrorCapture {\n  constructor(config, logger) {\n    this.config = config;\n    this.logger = logger;\n    this.originalConsoleError = console.error;\n    this.originalConsoleWarn = console.warn;\n    this.originalFetch = window.fetch;\n    this.originalXHROpen = XMLHttpRequest.prototype.open;\n    this.originalXHRSend = XMLHttpRequest.prototype.send;\n    \n    this.setupErrorHandlers();\n  }\n\n  /**\n   * Set up all error handlers\n   */\n  setupErrorHandlers() {\n    this.setupJavaScriptErrorHandler();\n    this.setupUnhandledRejectionHandler();\n    this.setupResourceErrorHandler();\n    \n    if (this.config.enableConsoleCapture) {\n      this.setupConsoleErrorHandler();\n    }\n    \n    if (this.config.enableNetworkCapture) {\n      this.setupNetworkErrorHandler();\n    }\n  }\n\n  /**\n   * Handle JavaScript errors\n   */\n  setupJavaScriptErrorHandler() {\n    window.addEventListener('error', (event) => {\n      const errorData = {\n        type: ERROR_TYPES.JAVASCRIPT,\n        message: event.message,\n        filename: event.filename,\n        lineno: event.lineno,\n        colno: event.colno,\n        stack: event.error ? event.error.stack : null,\n        timestamp: getCurrentTimestamp(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        severity: determineErrorSeverity({\n          message: event.message,\n          type: ERROR_TYPES.JAVASCRIPT\n        })\n      };\n      \n      this.logger.logError(errorData);\n    });\n  }\n\n  /**\n   * Handle unhandled promise rejections\n   */\n  setupUnhandledRejectionHandler() {\n    window.addEventListener('unhandledrejection', (event) => {\n      const errorData = {\n        type: ERROR_TYPES.UNHANDLED_REJECTION,\n        message: event.reason ? event.reason.toString() : 'Unhandled Promise Rejection',\n        stack: event.reason && event.reason.stack ? event.reason.stack : null,\n        timestamp: getCurrentTimestamp(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        severity: ERROR_SEVERITY.HIGH\n      };\n      \n      this.logger.logError(errorData);\n    });\n  }\n\n  /**\n   * Handle resource loading errors\n   */\n  setupResourceErrorHandler() {\n    if (!this.config.enableResourceCapture) return;\n    \n    window.addEventListener('error', (event) => {\n      // Only handle resource errors (not JavaScript errors)\n      if (event.target !== window && event.target.tagName) {\n        const errorData = {\n          type: ERROR_TYPES.RESOURCE,\n          message: `Failed to load ${event.target.tagName.toLowerCase()}: ${event.target.src || event.target.href}`,\n          resourceType: event.target.tagName.toLowerCase(),\n          resourceUrl: event.target.src || event.target.href,\n          timestamp: getCurrentTimestamp(),\n          url: window.location.href,\n          userAgent: navigator.userAgent,\n          severity: ERROR_SEVERITY.MEDIUM\n        };\n        \n        this.logger.logError(errorData);\n      }\n    }, true); // Use capture phase\n  }\n\n  /**\n   * Handle console errors\n   */\n  setupConsoleErrorHandler() {\n    const self = this;\n    \n    console.error = function(...args) {\n      const errorData = {\n        type: ERROR_TYPES.CONSOLE,\n        message: args.map(arg => \n          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n        ).join(' '),\n        timestamp: getCurrentTimestamp(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        severity: ERROR_SEVERITY.MEDIUM\n      };\n      \n      self.logger.logError(errorData);\n      \n      // Call original console.error\n      self.originalConsoleError.apply(console, args);\n    };\n    \n    console.warn = function(...args) {\n      if (self.config.verbose) {\n        const errorData = {\n          type: ERROR_TYPES.CONSOLE,\n          message: '[WARN] ' + args.map(arg => \n            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n          ).join(' '),\n          timestamp: getCurrentTimestamp(),\n          url: window.location.href,\n          userAgent: navigator.userAgent,\n          severity: ERROR_SEVERITY.LOW\n        };\n        \n        self.logger.logError(errorData);\n      }\n      \n      // Call original console.warn\n      self.originalConsoleWarn.apply(console, args);\n    };\n  }\n\n  /**\n   * Handle network errors\n   */\n  setupNetworkErrorHandler() {\n    this.setupFetchErrorHandler();\n    this.setupXHRErrorHandler();\n  }\n\n  /**\n   * Handle fetch API errors\n   */\n  setupFetchErrorHandler() {\n    const self = this;\n    \n    window.fetch = function(...args) {\n      return self.originalFetch.apply(this, args)\n        .then(response => {\n          if (!response.ok) {\n            const errorData = {\n              type: ERROR_TYPES.NETWORK,\n              message: `Fetch error: ${response.status} ${response.statusText}`,\n              url: args[0],\n              status: response.status,\n              statusText: response.statusText,\n              timestamp: getCurrentTimestamp(),\n              pageUrl: window.location.href,\n              userAgent: navigator.userAgent,\n              severity: response.status >= 500 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM\n            };\n            \n            self.logger.logError(errorData);\n          }\n          return response;\n        })\n        .catch(error => {\n          const errorData = {\n            type: ERROR_TYPES.NETWORK,\n            message: `Fetch error: ${error.message}`,\n            url: args[0],\n            error: error.toString(),\n            timestamp: getCurrentTimestamp(),\n            pageUrl: window.location.href,\n            userAgent: navigator.userAgent,\n            severity: ERROR_SEVERITY.HIGH\n          };\n          \n          self.logger.logError(errorData);\n          throw error;\n        });\n    };\n  }\n\n  /**\n   * Handle XMLHttpRequest errors\n   */\n  setupXHRErrorHandler() {\n    const self = this;\n    \n    XMLHttpRequest.prototype.open = function(method, url, ...args) {\n      this._rumMethod = method;\n      this._rumUrl = url;\n      return self.originalXHROpen.apply(this, [method, url, ...args]);\n    };\n    \n    XMLHttpRequest.prototype.send = function(...args) {\n      const xhr = this;\n      \n      xhr.addEventListener('error', function() {\n        const errorData = {\n          type: ERROR_TYPES.NETWORK,\n          message: `XHR error: ${xhr._rumMethod} ${xhr._rumUrl}`,\n          method: xhr._rumMethod,\n          url: xhr._rumUrl,\n          timestamp: getCurrentTimestamp(),\n          pageUrl: window.location.href,\n          userAgent: navigator.userAgent,\n          severity: ERROR_SEVERITY.HIGH\n        };\n        \n        self.logger.logError(errorData);\n      });\n      \n      xhr.addEventListener('load', function() {\n        if (xhr.status >= 400) {\n          const errorData = {\n            type: ERROR_TYPES.NETWORK,\n            message: `XHR error: ${xhr.status} ${xhr.statusText}`,\n            method: xhr._rumMethod,\n            url: xhr._rumUrl,\n            status: xhr.status,\n            statusText: xhr.statusText,\n            timestamp: getCurrentTimestamp(),\n            pageUrl: window.location.href,\n            userAgent: navigator.userAgent,\n            severity: xhr.status >= 500 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM\n          };\n          \n          self.logger.logError(errorData);\n        }\n      });\n      \n      return self.originalXHRSend.apply(this, args);\n    };\n  }\n\n  /**\n   * Manually log a custom error\n   */\n  logCustomError(message, details = {}) {\n    const errorData = {\n      type: ERROR_TYPES.CUSTOM,\n      message,\n      ...details,\n      timestamp: getCurrentTimestamp(),\n      url: window.location.href,\n      userAgent: navigator.userAgent,\n      severity: details.severity || ERROR_SEVERITY.MEDIUM\n    };\n    \n    this.logger.logError(errorData);\n  }\n\n  /**\n   * Clean up error handlers\n   */\n  destroy() {\n    // Restore original functions\n    console.error = this.originalConsoleError;\n    console.warn = this.originalConsoleWarn;\n    window.fetch = this.originalFetch;\n    XMLHttpRequest.prototype.open = this.originalXHROpen;\n    XMLHttpRequest.prototype.send = this.originalXHRSend;\n  }\n}\n", "/**\n * Error logging functionality for RUM Script\n */\n\nimport { sanitizeErrorData, shouldIgnoreError, debounce } from './utils.js';\n\nexport class ErrorLogger {\n  constructor(config, sessionId) {\n    this.config = config;\n    this.sessionId = sessionId;\n    this.errorQueue = [];\n    this.errorCount = 0;\n    this.lastErrorTime = null;\n    this.retryQueue = [];\n\n    // Debounced batch send function\n    this.debouncedSend = debounce(() => this.sendBatch(), this.config.batchTimeout);\n\n    // Set up periodic batch sending\n    this.batchInterval = setInterval(() => {\n      if (this.errorQueue.length > 0) {\n        this.sendBatch();\n      }\n    }, this.config.batchTimeout);\n  }\n\n  /**\n   * Log an error\n   */\n  logError(errorData) {\n    // Check if logging is enabled\n    if (!this.config.enableLogging) return;\n\n    // Check if error should be ignored\n    if (shouldIgnoreError(errorData, this.config)) return;\n\n    // Check if we've exceeded max errors per session\n    if (this.errorCount >= this.config.maxErrorsPerSession) {\n      if (this.config.debug) {\n        console.warn('RUM: Max errors per session exceeded');\n      }\n      return;\n    }\n\n    // Sanitize error data\n    const sanitizedError = sanitizeErrorData(errorData, this.config);\n\n    // Add session information\n    const enrichedError = {\n      ...sanitizedError,\n      sessionId: this.sessionId,\n      errorId: this.generateErrorId(),\n      environment: this.config.environment,\n      viewport: {\n        width: window.innerWidth,\n        height: window.innerHeight\n      },\n      screen: {\n        width: window.screen.width,\n        height: window.screen.height\n      }\n    };\n\n    // Add to queue\n    this.errorQueue.push(enrichedError);\n    this.errorCount++;\n    this.lastErrorTime = Date.now();\n\n    if (this.config.debug) {\n      console.log('RUM: Error logged', enrichedError);\n    }\n\n    // Send immediately if batch size reached or critical error\n    if (this.errorQueue.length >= this.config.batchSize ||\n      errorData.severity === 'critical') {\n      this.sendBatch();\n    } else {\n      // Otherwise, debounce the send\n      this.debouncedSend();\n    }\n  }\n\n  /**\n   * Send batch of errors to server\n   */\n  async sendBatch() {\n    if (this.errorQueue.length === 0) return;\n\n    const batch = [...this.errorQueue];\n    this.errorQueue = [];\n\n    const payload = {\n      errors: batch,\n      sessionId: this.sessionId,\n      projectId: this.config.projectId,\n      timestamp: new Date().toISOString(),\n      batchId: this.generateBatchId()\n    };\n\n    try {\n      await this.sendToServer(payload);\n\n      if (this.config.debug) {\n        console.log('RUM: Batch sent successfully', payload);\n      }\n    } catch (error) {\n      if (this.config.debug) {\n        console.error('RUM: Failed to send batch', error);\n      }\n\n      // Add to retry queue\n      this.retryQueue.push({\n        payload,\n        attempts: 0,\n        timestamp: Date.now()\n      });\n\n      this.processRetryQueue();\n    }\n  }\n\n  /**\n   * Send payload to server\n   */\n  async sendToServer(payload) {\n    const url = `${this.config.baseUrl}${this.config.logEndpoint}`;\n\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n\n    if (this.config.apiKey) {\n      headers['Authorization'] = `Bearer ${this.config.apiKey}`;\n    }\n\n    const response = await fetch(url, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(payload),\n      mode: 'cors'\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    return response.json();\n  }\n\n  /**\n   * Process retry queue\n   */\n  async processRetryQueue() {\n    const now = Date.now();\n    const itemsToRetry = [];\n\n    this.retryQueue = this.retryQueue.filter(item => {\n      if (item.attempts >= this.config.maxRetries) {\n        if (this.config.debug) {\n          console.warn('RUM: Max retries exceeded for batch', item.payload.batchId);\n        }\n        return false; // Remove from queue\n      }\n\n      const timeSinceLastAttempt = now - item.timestamp;\n      const retryDelay = this.config.retryDelay * Math.pow(2, item.attempts); // Exponential backoff\n\n      if (timeSinceLastAttempt >= retryDelay) {\n        itemsToRetry.push(item);\n        return false; // Remove from queue (will be re-added if retry fails)\n      }\n\n      return true; // Keep in queue\n    });\n\n    // Process retry items\n    for (const item of itemsToRetry) {\n      try {\n        await this.sendToServer(item.payload);\n\n        if (this.config.debug) {\n          console.log('RUM: Retry successful for batch', item.payload.batchId);\n        }\n      } catch (error) {\n        if (this.config.debug) {\n          console.error('RUM: Retry failed for batch', item.payload.batchId, error);\n        }\n\n        // Add back to retry queue with incremented attempts\n        this.retryQueue.push({\n          ...item,\n          attempts: item.attempts + 1,\n          timestamp: now\n        });\n      }\n    }\n\n    // Schedule next retry processing if there are items in queue\n    if (this.retryQueue.length > 0) {\n      setTimeout(() => this.processRetryQueue(), this.config.retryDelay);\n    }\n  }\n\n  /**\n   * Generate unique error ID\n   */\n  generateErrorId() {\n    return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n  }\n\n  /**\n   * Generate unique batch ID\n   */\n  generateBatchId() {\n    return 'batch_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n  }\n\n  /**\n   * Get error statistics\n   */\n  getStats() {\n    return {\n      totalErrors: this.errorCount,\n      queuedErrors: this.errorQueue.length,\n      retryQueueSize: this.retryQueue.length,\n      lastErrorTime: this.lastErrorTime,\n      sessionId: this.sessionId\n    };\n  }\n\n  /**\n   * Clear error queue\n   */\n  clearQueue() {\n    this.errorQueue = [];\n    this.retryQueue = [];\n  }\n\n  /**\n   * Destroy logger and clean up\n   */\n  destroy() {\n    // Send any remaining errors\n    if (this.errorQueue.length > 0) {\n      this.sendBatch();\n    }\n\n    // Clear intervals\n    if (this.batchInterval) {\n      clearInterval(this.batchInterval);\n    }\n\n    // Clear queues\n    this.clearQueue();\n  }\n}\n", "/**\n * User interface for error alerts and recovery options\n */\n\nimport { ALERT_TYPES, ERROR_SEVERITY } from './config.js';\n\nexport class UserInterface {\n  constructor(config) {\n    this.config = config;\n    this.alertContainer = null;\n    this.activeAlerts = new Map();\n    this.alertCounter = 0;\n    \n    this.createAlertContainer();\n    this.injectStyles();\n  }\n\n  /**\n   * Create alert container\n   */\n  createAlertContainer() {\n    if (!this.config.showUserAlerts) return;\n    \n    this.alertContainer = document.createElement('div');\n    this.alertContainer.id = 'rum-alert-container';\n    this.alertContainer.className = `rum-alerts rum-alerts-${this.config.alertPosition}`;\n    \n    document.body.appendChild(this.alertContainer);\n  }\n\n  /**\n   * Inject CSS styles\n   */\n  injectStyles() {\n    if (!this.config.showUserAlerts) return;\n    \n    const styles = `\n      .rum-alerts {\n        position: fixed;\n        z-index: 10000;\n        pointer-events: none;\n        max-width: 400px;\n      }\n      \n      .rum-alerts-top-right {\n        top: 20px;\n        right: 20px;\n      }\n      \n      .rum-alerts-top-left {\n        top: 20px;\n        left: 20px;\n      }\n      \n      .rum-alerts-bottom-right {\n        bottom: 20px;\n        right: 20px;\n      }\n      \n      .rum-alerts-bottom-left {\n        bottom: 20px;\n        left: 20px;\n      }\n      \n      .rum-alert {\n        background: white;\n        border-radius: 8px;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        margin-bottom: 12px;\n        padding: 16px;\n        pointer-events: auto;\n        transform: translateX(100%);\n        transition: all 0.3s ease;\n        border-left: 4px solid #ccc;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        line-height: 1.4;\n      }\n      \n      .rum-alert.rum-alert-visible {\n        transform: translateX(0);\n      }\n      \n      .rum-alert-error {\n        border-left-color: #ef4444;\n      }\n      \n      .rum-alert-warning {\n        border-left-color: #f59e0b;\n      }\n      \n      .rum-alert-info {\n        border-left-color: #3b82f6;\n      }\n      \n      .rum-alert-success {\n        border-left-color: #10b981;\n      }\n      \n      .rum-alert-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-bottom: 8px;\n      }\n      \n      .rum-alert-title {\n        font-weight: 600;\n        color: #1f2937;\n        margin: 0;\n      }\n      \n      .rum-alert-close {\n        background: none;\n        border: none;\n        font-size: 18px;\n        cursor: pointer;\n        color: #6b7280;\n        padding: 0;\n        width: 20px;\n        height: 20px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      \n      .rum-alert-close:hover {\n        color: #374151;\n      }\n      \n      .rum-alert-message {\n        color: #4b5563;\n        margin-bottom: 12px;\n      }\n      \n      .rum-alert-actions {\n        display: flex;\n        gap: 8px;\n        flex-wrap: wrap;\n      }\n      \n      .rum-alert-button {\n        background: #f3f4f6;\n        border: 1px solid #d1d5db;\n        border-radius: 4px;\n        padding: 6px 12px;\n        font-size: 12px;\n        cursor: pointer;\n        transition: background-color 0.2s;\n      }\n      \n      .rum-alert-button:hover {\n        background: #e5e7eb;\n      }\n      \n      .rum-alert-button-primary {\n        background: #3b82f6;\n        color: white;\n        border-color: #3b82f6;\n      }\n      \n      .rum-alert-button-primary:hover {\n        background: #2563eb;\n      }\n    `;\n    \n    const styleSheet = document.createElement('style');\n    styleSheet.textContent = styles;\n    document.head.appendChild(styleSheet);\n  }\n\n  /**\n   * Show error alert to user\n   */\n  showErrorAlert(errorData) {\n    if (!this.config.showUserAlerts || !this.alertContainer) return;\n    \n    const alertType = this.getAlertTypeFromSeverity(errorData.severity);\n    const alertId = ++this.alertCounter;\n    \n    const alert = this.createAlert({\n      id: alertId,\n      type: alertType,\n      title: this.getErrorTitle(errorData),\n      message: this.getErrorMessage(errorData),\n      actions: this.getErrorActions(errorData)\n    });\n    \n    this.alertContainer.appendChild(alert);\n    this.activeAlerts.set(alertId, alert);\n    \n    // Show alert with animation\n    setTimeout(() => {\n      alert.classList.add('rum-alert-visible');\n    }, 10);\n    \n    // Auto-hide after duration (unless it's critical)\n    if (errorData.severity !== ERROR_SEVERITY.CRITICAL) {\n      setTimeout(() => {\n        this.hideAlert(alertId);\n      }, this.config.alertDuration);\n    }\n    \n    return alertId;\n  }\n\n  /**\n   * Create alert element\n   */\n  createAlert({ id, type, title, message, actions }) {\n    const alert = document.createElement('div');\n    alert.className = `rum-alert rum-alert-${type}`;\n    alert.dataset.alertId = id;\n    \n    const header = document.createElement('div');\n    header.className = 'rum-alert-header';\n    \n    const titleElement = document.createElement('h4');\n    titleElement.className = 'rum-alert-title';\n    titleElement.textContent = title;\n    \n    const closeButton = document.createElement('button');\n    closeButton.className = 'rum-alert-close';\n    closeButton.innerHTML = '×';\n    closeButton.onclick = () => this.hideAlert(id);\n    \n    header.appendChild(titleElement);\n    header.appendChild(closeButton);\n    \n    const messageElement = document.createElement('div');\n    messageElement.className = 'rum-alert-message';\n    messageElement.textContent = message;\n    \n    alert.appendChild(header);\n    alert.appendChild(messageElement);\n    \n    if (actions && actions.length > 0) {\n      const actionsContainer = document.createElement('div');\n      actionsContainer.className = 'rum-alert-actions';\n      \n      actions.forEach(action => {\n        const button = document.createElement('button');\n        button.className = `rum-alert-button ${action.primary ? 'rum-alert-button-primary' : ''}`;\n        button.textContent = action.text;\n        button.onclick = () => {\n          action.handler();\n          if (action.closeOnClick !== false) {\n            this.hideAlert(id);\n          }\n        };\n        actionsContainer.appendChild(button);\n      });\n      \n      alert.appendChild(actionsContainer);\n    }\n    \n    return alert;\n  }\n\n  /**\n   * Hide alert\n   */\n  hideAlert(alertId) {\n    const alert = this.activeAlerts.get(alertId);\n    if (!alert) return;\n    \n    alert.classList.remove('rum-alert-visible');\n    \n    setTimeout(() => {\n      if (alert.parentNode) {\n        alert.parentNode.removeChild(alert);\n      }\n      this.activeAlerts.delete(alertId);\n    }, 300);\n  }\n\n  /**\n   * Get alert type from error severity\n   */\n  getAlertTypeFromSeverity(severity) {\n    switch (severity) {\n      case ERROR_SEVERITY.CRITICAL:\n        return ALERT_TYPES.ERROR;\n      case ERROR_SEVERITY.HIGH:\n        return ALERT_TYPES.ERROR;\n      case ERROR_SEVERITY.MEDIUM:\n        return ALERT_TYPES.WARNING;\n      case ERROR_SEVERITY.LOW:\n        return ALERT_TYPES.INFO;\n      default:\n        return ALERT_TYPES.INFO;\n    }\n  }\n\n  /**\n   * Get error title for display\n   */\n  getErrorTitle(errorData) {\n    switch (errorData.severity) {\n      case ERROR_SEVERITY.CRITICAL:\n        return 'Critical Error Detected';\n      case ERROR_SEVERITY.HIGH:\n        return 'Error Occurred';\n      case ERROR_SEVERITY.MEDIUM:\n        return 'Warning';\n      case ERROR_SEVERITY.LOW:\n        return 'Notice';\n      default:\n        return 'Error';\n    }\n  }\n\n  /**\n   * Get user-friendly error message\n   */\n  getErrorMessage(errorData) {\n    switch (errorData.type) {\n      case 'javascript':\n        return 'A JavaScript error occurred that may affect functionality.';\n      case 'network':\n        return 'A network request failed. Please check your connection.';\n      case 'resource':\n        return 'A resource failed to load properly.';\n      default:\n        return 'An unexpected error occurred.';\n    }\n  }\n\n  /**\n   * Get error recovery actions\n   */\n  getErrorActions(errorData) {\n    const actions = [];\n    \n    // Always provide reload option for critical errors\n    if (errorData.severity === ERROR_SEVERITY.CRITICAL) {\n      actions.push({\n        text: 'Reload Page',\n        primary: true,\n        handler: () => window.location.reload()\n      });\n    }\n    \n    // Retry action for network errors\n    if (errorData.type === 'network') {\n      actions.push({\n        text: 'Retry',\n        primary: true,\n        handler: () => {\n          // Trigger a custom retry event\n          window.dispatchEvent(new CustomEvent('rum:retry', {\n            detail: { errorData }\n          }));\n        }\n      });\n    }\n    \n    // Contact support action\n    actions.push({\n      text: 'Contact Support',\n      handler: () => {\n        // Trigger a custom support event\n        window.dispatchEvent(new CustomEvent('rum:contact-support', {\n          detail: { errorData }\n        }));\n      }\n    });\n    \n    return actions;\n  }\n\n  /**\n   * Show success message\n   */\n  showSuccess(message) {\n    if (!this.config.showUserAlerts || !this.alertContainer) return;\n    \n    const alertId = ++this.alertCounter;\n    const alert = this.createAlert({\n      id: alertId,\n      type: ALERT_TYPES.SUCCESS,\n      title: 'Success',\n      message,\n      actions: []\n    });\n    \n    this.alertContainer.appendChild(alert);\n    this.activeAlerts.set(alertId, alert);\n    \n    setTimeout(() => {\n      alert.classList.add('rum-alert-visible');\n    }, 10);\n    \n    setTimeout(() => {\n      this.hideAlert(alertId);\n    }, 3000);\n    \n    return alertId;\n  }\n\n  /**\n   * Clear all alerts\n   */\n  clearAllAlerts() {\n    this.activeAlerts.forEach((alert, id) => {\n      this.hideAlert(id);\n    });\n  }\n\n  /**\n   * Destroy UI and clean up\n   */\n  destroy() {\n    this.clearAllAlerts();\n    \n    if (this.alertContainer && this.alertContainer.parentNode) {\n      this.alertContainer.parentNode.removeChild(this.alertContainer);\n    }\n  }\n}\n", "/**\n * RUM Script - Real User Monitoring with Error Logging and Alerting\n */\n\nimport { DEFAULT_CONFIG, ERROR_SEVERITY } from './config.js';\nimport { generateSessionId, deepMerge, throttle } from './utils.js';\nimport { ErrorCapture } from './errorCapture.js';\nimport { ErrorLogger } from './errorLogger.js';\nimport { UserInterface } from './userInterface.js';\n\nexport class RUMScript {\n  constructor(userConfig = {}) {\n    // Merge user config with defaults\n    this.config = deepMerge(DEFAULT_CONFIG, userConfig);\n    \n    // Generate session ID\n    this.sessionId = generateSessionId();\n    \n    // Initialize components\n    this.logger = null;\n    this.errorCapture = null;\n    this.ui = null;\n    \n    // Error tracking\n    this.errorCounts = new Map();\n    this.lastAlertTime = 0;\n    \n    // Throttled alert function\n    this.throttledAlert = throttle((errorData) => {\n      this.handleCriticalError(errorData);\n    }, 5000); // Max one alert per 5 seconds\n    \n    // Initialize if DOM is ready\n    if (document.readyState === 'loading') {\n      document.addEventListener('DOMContentLoaded', () => this.initialize());\n    } else {\n      this.initialize();\n    }\n  }\n\n  /**\n   * Initialize RUM script\n   */\n  initialize() {\n    try {\n      if (this.config.debug) {\n        console.log('RUM: Initializing with config', this.config);\n      }\n      \n      // Initialize logger\n      this.logger = new ErrorLogger(this.config, this.sessionId);\n      \n      // Initialize UI\n      this.ui = new UserInterface(this.config);\n      \n      // Initialize error capture with custom error handler\n      this.errorCapture = new ErrorCapture(this.config, {\n        logError: (errorData) => this.handleError(errorData)\n      });\n      \n      // Set up page visibility change handler\n      this.setupPageVisibilityHandler();\n      \n      // Set up beforeunload handler\n      this.setupBeforeUnloadHandler();\n      \n      // Set up custom event listeners\n      this.setupCustomEventListeners();\n      \n      if (this.config.debug) {\n        console.log('RUM: Initialized successfully');\n      }\n      \n      // Expose global methods\n      this.exposeGlobalMethods();\n      \n    } catch (error) {\n      console.error('RUM: Failed to initialize', error);\n    }\n  }\n\n  /**\n   * Handle captured errors\n   */\n  handleError(errorData) {\n    // Log error\n    this.logger.logError(errorData);\n    \n    // Track error frequency\n    this.trackErrorFrequency(errorData);\n    \n    // Show user alert for critical/high severity errors\n    if (errorData.severity === ERROR_SEVERITY.CRITICAL || \n        errorData.severity === ERROR_SEVERITY.HIGH) {\n      this.ui.showErrorAlert(errorData);\n    }\n    \n    // Check for alert threshold\n    if (this.shouldTriggerAlert()) {\n      this.throttledAlert(errorData);\n    }\n  }\n\n  /**\n   * Track error frequency for alerting\n   */\n  trackErrorFrequency(errorData) {\n    const now = Date.now();\n    const timeWindow = this.config.timeWindow;\n    \n    // Clean old entries\n    for (const [timestamp, count] of this.errorCounts.entries()) {\n      if (now - timestamp > timeWindow) {\n        this.errorCounts.delete(timestamp);\n      }\n    }\n    \n    // Add current error\n    const windowStart = Math.floor(now / timeWindow) * timeWindow;\n    const currentCount = this.errorCounts.get(windowStart) || 0;\n    this.errorCounts.set(windowStart, currentCount + 1);\n  }\n\n  /**\n   * Check if alert threshold is exceeded\n   */\n  shouldTriggerAlert() {\n    const totalErrors = Array.from(this.errorCounts.values())\n      .reduce((sum, count) => sum + count, 0);\n    \n    return totalErrors >= this.config.errorThreshold;\n  }\n\n  /**\n   * Handle critical errors that require immediate attention\n   */\n  handleCriticalError(errorData) {\n    const now = Date.now();\n    \n    // Prevent spam alerts\n    if (now - this.lastAlertTime < 60000) { // 1 minute cooldown\n      return;\n    }\n    \n    this.lastAlertTime = now;\n    \n    // Trigger custom event for external handling\n    window.dispatchEvent(new CustomEvent('rum:critical-error', {\n      detail: {\n        errorData,\n        sessionId: this.sessionId,\n        errorCount: Array.from(this.errorCounts.values())\n          .reduce((sum, count) => sum + count, 0)\n      }\n    }));\n    \n    if (this.config.debug) {\n      console.warn('RUM: Critical error threshold exceeded', errorData);\n    }\n  }\n\n  /**\n   * Set up page visibility change handler\n   */\n  setupPageVisibilityHandler() {\n    document.addEventListener('visibilitychange', () => {\n      if (document.hidden) {\n        // Page is hidden, flush any pending errors\n        this.logger.sendBatch();\n      }\n    });\n  }\n\n  /**\n   * Set up beforeunload handler\n   */\n  setupBeforeUnloadHandler() {\n    window.addEventListener('beforeunload', () => {\n      // Send any remaining errors before page unload\n      this.logger.sendBatch();\n    });\n  }\n\n  /**\n   * Set up custom event listeners\n   */\n  setupCustomEventListeners() {\n    // Retry event handler\n    window.addEventListener('rum:retry', (event) => {\n      const { errorData } = event.detail;\n      \n      if (this.config.debug) {\n        console.log('RUM: Retry requested for error', errorData);\n      }\n      \n      // Show success message after retry\n      setTimeout(() => {\n        this.ui.showSuccess('Retry completed successfully');\n      }, 1000);\n    });\n    \n    // Contact support event handler\n    window.addEventListener('rum:contact-support', (event) => {\n      const { errorData } = event.detail;\n      \n      if (this.config.debug) {\n        console.log('RUM: Support contact requested for error', errorData);\n      }\n      \n      // You can customize this to open a support form, email, etc.\n      const supportUrl = `mailto:<EMAIL>?subject=Error Report&body=Error ID: ${errorData.errorId}%0ASession ID: ${this.sessionId}%0AError: ${encodeURIComponent(errorData.message)}`;\n      window.open(supportUrl);\n    });\n  }\n\n  /**\n   * Expose global methods for external use\n   */\n  exposeGlobalMethods() {\n    // Make RUM methods available globally\n    window.RUM = {\n      // Log custom error\n      logError: (message, details = {}) => {\n        this.errorCapture.logCustomError(message, details);\n      },\n      \n      // Get error statistics\n      getStats: () => {\n        return {\n          ...this.logger.getStats(),\n          config: this.config,\n          errorFrequency: Object.fromEntries(this.errorCounts)\n        };\n      },\n      \n      // Update configuration\n      updateConfig: (newConfig) => {\n        this.config = deepMerge(this.config, newConfig);\n        if (this.config.debug) {\n          console.log('RUM: Configuration updated', this.config);\n        }\n      },\n      \n      // Clear error queue\n      clearErrors: () => {\n        this.logger.clearQueue();\n        this.errorCounts.clear();\n        this.ui.clearAllAlerts();\n      },\n      \n      // Destroy RUM instance\n      destroy: () => {\n        this.destroy();\n      }\n    };\n  }\n\n  /**\n   * Get current statistics\n   */\n  getStats() {\n    return {\n      sessionId: this.sessionId,\n      config: this.config,\n      logger: this.logger ? this.logger.getStats() : null,\n      errorFrequency: Object.fromEntries(this.errorCounts),\n      lastAlertTime: this.lastAlertTime\n    };\n  }\n\n  /**\n   * Update configuration\n   */\n  updateConfig(newConfig) {\n    this.config = deepMerge(this.config, newConfig);\n    \n    if (this.config.debug) {\n      console.log('RUM: Configuration updated', this.config);\n    }\n  }\n\n  /**\n   * Destroy RUM instance and clean up\n   */\n  destroy() {\n    if (this.config.debug) {\n      console.log('RUM: Destroying instance');\n    }\n    \n    // Clean up components\n    if (this.errorCapture) {\n      this.errorCapture.destroy();\n    }\n    \n    if (this.logger) {\n      this.logger.destroy();\n    }\n    \n    if (this.ui) {\n      this.ui.destroy();\n    }\n    \n    // Clear data\n    this.errorCounts.clear();\n    \n    // Remove global methods\n    if (window.RUM) {\n      delete window.RUM;\n    }\n  }\n}\n\n// Auto-initialize if config is provided via data attributes\ndocument.addEventListener('DOMContentLoaded', () => {\n  const scriptTag = document.querySelector('script[data-rum-config]');\n  if (scriptTag) {\n    try {\n      const config = JSON.parse(scriptTag.dataset.rumConfig);\n      new RUMScript(config);\n    } catch (error) {\n      console.error('RUM: Failed to parse config from script tag', error);\n    }\n  }\n});\n\n// Export for module usage\nexport default RUMScript;\n"], "names": ["DEFAULT_CONFIG", "logEndpoint", "baseUrl", "projectId", "errorT<PERSON>eshold", "timeWindow", "environment", "enableLogging", "enableConsoleCapture", "enableNetworkCapture", "enableResourceCapture", "maxErrorsPerSession", "batchSize", "batchTimeout", "showUserAlerts", "alertPosition", "alertDuration", "<PERSON><PERSON><PERSON><PERSON>", "sanitizeData", "excludeUrls", "excludeMessages", "maxRetries", "retry<PERSON><PERSON><PERSON>", "debug", "verbose", "ERROR_SEVERITY", "ERROR_TYPES", "ALERT_TYPES", "getCurrentTimestamp", "Date", "toISOString", "determineErrorSeverity", "error", "message", "includes", "deepMerge", "target", "source", "output", "Object", "assign", "isObject", "keys", "for<PERSON>ach", "key", "item", "Array", "isArray", "ErrorCapture", "constructor", "config", "logger", "this", "originalConsoleError", "console", "originalConsoleWarn", "warn", "originalFetch", "window", "fetch", "originalXHROpen", "XMLHttpRequest", "prototype", "open", "originalXHRSend", "send", "setupErrorHandlers", "setupJavaScriptErrorHandler", "setupUnhandledRejectionHandler", "setupResourceErrorHandler", "setupConsoleErrorHandler", "setupNetworkErrorHandler", "addEventListener", "event", "errorData", "type", "filename", "lineno", "colno", "stack", "timestamp", "url", "location", "href", "userAgent", "navigator", "severity", "logError", "reason", "toString", "tagName", "toLowerCase", "src", "resourceType", "resourceUrl", "self", "args", "map", "arg", "JSON", "stringify", "String", "join", "apply", "setupFetchErrorHandler", "setupXHRErrorHandler", "then", "response", "ok", "status", "statusText", "pageUrl", "catch", "method", "_rumMethod", "_rumUrl", "xhr", "logCustomError", "details", "destroy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sessionId", "errorQueue", "errorCount", "lastErrorTime", "retryQueue", "debouncedSend", "func", "wait", "timeout", "clearTimeout", "setTimeout", "debounce", "sendBatch", "batchInterval", "setInterval", "length", "currentUrl", "some", "pattern", "RegExp", "test", "shouldIgnoreError", "enrichedError", "data", "sanitized", "replace", "URL", "param", "searchParams", "has", "set", "e", "sanitizeErrorData", "errorId", "generateErrorId", "viewport", "width", "innerWidth", "height", "innerHeight", "screen", "push", "now", "log", "batch", "payload", "errors", "batchId", "generateBatchId", "sendToServer", "attempts", "processRetryQueue", "headers", "body", "mode", "Error", "json", "itemsToRetry", "filter", "Math", "pow", "random", "substr", "getStats", "totalErrors", "queuedErrors", "retryQueueSize", "clearQueue", "clearInterval", "UserInterface", "alertContainer", "active<PERSON>lerts", "Map", "alertCounter", "createAlertContainer", "injectStyles", "document", "createElement", "id", "className", "append<PERSON><PERSON><PERSON>", "styleSheet", "textContent", "head", "showError<PERSON><PERSON>t", "alertType", "getAlertTypeFromSeverity", "alertId", "alert", "createAlert", "title", "getErrorTitle", "getErrorMessage", "actions", "getErrorActions", "classList", "add", "<PERSON><PERSON><PERSON><PERSON>", "dataset", "header", "titleElement", "closeButton", "innerHTML", "onclick", "messageElement", "actionsContainer", "action", "button", "primary", "text", "handler", "closeOnClick", "get", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "delete", "reload", "dispatchEvent", "CustomEvent", "detail", "showSuccess", "clearAll<PERSON>ler<PERSON>", "RUMScript", "userConfig", "errorCapture", "ui", "errorCounts", "lastAlertTime", "throt<PERSON><PERSON><PERSON><PERSON>", "limit", "inThrottle", "arguments", "context", "throttle", "handleCriticalError", "readyState", "initialize", "handleError", "setupPageVisibilityHandler", "setupBeforeUnloadHandler", "setupCustomEventListeners", "exposeGlobalMethods", "trackErrorFrequency", "shouldTriggerAlert", "count", "entries", "windowStart", "floor", "currentCount", "from", "values", "reduce", "sum", "hidden", "supportUrl", "encodeURIComponent", "RUM", "errorFrequency", "fromEntries", "updateConfig", "newConfig", "clearErrors", "clear", "scriptTag", "querySelector", "parse", "rumConfig"], "mappings": "gPAGO,MAAMA,EAAiB,CAE5BC,YAAa,cACbC,QAAS,wBAGTC,UAAW,mBAGXC,eAAgB,EAChBC,WAAY,IAGZC,YAAa,aACbC,eAAe,EACfC,sBAAsB,EACtBC,sBAAsB,EACtBC,uBAAuB,EAGvBC,oBAAqB,IACrBC,UAAW,GACXC,aAAc,IAGdC,gBAAgB,EAChBC,cAAe,YACfC,cAAe,IAGfC,OAAQ,qBACRC,cAAc,EACdC,YAAa,GACbC,gBAAiB,GAGjBC,WAAY,EACZC,WAAY,IAGZC,OAAO,EACPC,SAAS,GAMEC,EACN,MADMA,EAEH,SAFGA,EAGL,OAHKA,EAID,WAMCC,EACC,aADDA,EAEF,UAFEA,EAGD,WAHCA,EAIF,UAJEA,EAKU,sBALVA,EAMH,SAMGC,EACJ,QADIA,EAEF,UAFEA,EAGL,OAHKA,EAIF,UC9DJ,SAASC,IACd,OAAO,IAAIC,MAAOC,aACpB,CA4EO,SAASC,EAAuBC,GAErC,MAAMP,EACC,MADDA,EAIM,WAIZ,OAAIO,EAAMC,UACRD,EAAMC,QAAQC,SAAS,mBACvBF,EAAMC,QAAQC,SAAS,kBACvBF,EAAMC,QAAQC,SAAS,yBACvBF,EAAMC,QAAQC,SAAS,sBAEhBT,EAcFA,CACT,CAoCO,SAASU,EAAUC,EAAQC,GAChC,MAAMC,EAASC,OAAOC,OAAO,CAAA,EAAIJ,GAajC,OAZIK,EAASL,IAAWK,EAASJ,IAC/BE,OAAOG,KAAKL,GAAQM,QAAQC,IACtBH,EAASJ,EAAOO,IACZA,KAAOR,EAGXE,EAAOM,GAAOT,EAAUC,EAAOQ,GAAMP,EAAOO,IAF5CL,OAAOC,OAAOF,EAAQ,CAAEM,CAACA,GAAMP,EAAOO,KAIxCL,OAAOC,OAAOF,EAAQ,CAAEM,CAACA,GAAMP,EAAOO,OAIrCN,CACT,CAEA,SAASG,EAASI,GAChB,OAAOA,GAAwB,iBAATA,IAAsBC,MAAMC,QAAQF,EAC5D,CC3KO,MAAMG,EACX,WAAAC,CAAYC,EAAQC,GAClBC,KAAKF,OAASA,EACdE,KAAKD,OAASA,EACdC,KAAKC,qBAAuBC,QAAQtB,MACpCoB,KAAKG,oBAAsBD,QAAQE,KACnCJ,KAAKK,cAAgBC,OAAOC,MAC5BP,KAAKQ,gBAAkBC,eAAeC,UAAUC,KAChDX,KAAKY,gBAAkBH,eAAeC,UAAUG,KAEhDb,KAAKc,oBACP,CAKA,kBAAAA,GACEd,KAAKe,8BACLf,KAAKgB,iCACLhB,KAAKiB,4BAEDjB,KAAKF,OAAO1C,sBACd4C,KAAKkB,2BAGHlB,KAAKF,OAAOzC,sBACd2C,KAAKmB,0BAET,CAKA,2BAAAJ,GACET,OAAOc,iBAAiB,QAAUC,IAChC,MAAMC,EAAY,CAChBC,KAAMjD,EACNO,QAASwC,EAAMxC,QACf2C,SAAUH,EAAMG,SAChBC,OAAQJ,EAAMI,OACdC,MAAOL,EAAMK,MACbC,MAAON,EAAMzC,MAAQyC,EAAMzC,MAAM+C,MAAQ,KACzCC,UAAWpD,IACXqD,IAAKvB,OAAOwB,SAASC,KACrBC,UAAWC,UAAUD,UACrBE,SAAUvD,EAAuB,CAC/BE,QAASwC,EAAMxC,WAKnBmB,KAAKD,OAAOoC,SAASb,IAEzB,CAKA,8BAAAN,GACEV,OAAOc,iBAAiB,qBAAuBC,IAC7C,MAAMC,EAAY,CAChBC,KAAMjD,EACNO,QAASwC,EAAMe,OAASf,EAAMe,OAAOC,WAAa,8BAClDV,MAAON,EAAMe,QAAUf,EAAMe,OAAOT,MAAQN,EAAMe,OAAOT,MAAQ,KACjEC,UAAWpD,IACXqD,IAAKvB,OAAOwB,SAASC,KACrBC,UAAWC,UAAUD,UACrBE,SAAU7D,GAGZ2B,KAAKD,OAAOoC,SAASb,IAEzB,CAKA,yBAAAL,GACOjB,KAAKF,OAAOxC,uBAEjBgD,OAAOc,iBAAiB,QAAUC,IAEhC,GAAIA,EAAMrC,SAAWsB,QAAUe,EAAMrC,OAAOsD,QAAS,CACnD,MAAMhB,EAAY,CAChBC,KAAMjD,EACNO,QAAS,kBAAkBwC,EAAMrC,OAAOsD,QAAQC,kBAAkBlB,EAAMrC,OAAOwD,KAAOnB,EAAMrC,OAAO+C,OACnGU,aAAcpB,EAAMrC,OAAOsD,QAAQC,cACnCG,YAAarB,EAAMrC,OAAOwD,KAAOnB,EAAMrC,OAAO+C,KAC9CH,UAAWpD,IACXqD,IAAKvB,OAAOwB,SAASC,KACrBC,UAAWC,UAAUD,UACrBE,SAAU7D,GAGZ2B,KAAKD,OAAOoC,SAASb,EACvB,IACC,EACL,CAKA,wBAAAJ,GACE,MAAMyB,EAAO3C,KAEbE,QAAQtB,MAAQ,YAAYgE,GAC1B,MAAMtB,EAAY,CAChBC,KAAMjD,EACNO,QAAS+D,EAAKC,IAAIC,GACD,iBAARA,EAAmBC,KAAKC,UAAUF,GAAOG,OAAOH,IACvDI,KAAK,KACPtB,UAAWpD,IACXqD,IAAKvB,OAAOwB,SAASC,KACrBC,UAAWC,UAAUD,UACrBE,SAAU7D,GAGZsE,EAAK5C,OAAOoC,SAASb,GAGrBqB,EAAK1C,qBAAqBkD,MAAMjD,QAAS0C,EAC3C,EAEA1C,QAAQE,KAAO,YAAYwC,GACzB,GAAID,EAAK7C,OAAO1B,QAAS,CACvB,MAAMkD,EAAY,CAChBC,KAAMjD,EACNO,QAAS,UAAY+D,EAAKC,IAAIC,GACb,iBAARA,EAAmBC,KAAKC,UAAUF,GAAOG,OAAOH,IACvDI,KAAK,KACPtB,UAAWpD,IACXqD,IAAKvB,OAAOwB,SAASC,KACrBC,UAAWC,UAAUD,UACrBE,SAAU7D,GAGZsE,EAAK5C,OAAOoC,SAASb,EACvB,CAGAqB,EAAKxC,oBAAoBgD,MAAMjD,QAAS0C,EAC1C,CACF,CAKA,wBAAAzB,GACEnB,KAAKoD,yBACLpD,KAAKqD,sBACP,CAKA,sBAAAD,GACE,MAAMT,EAAO3C,KAEbM,OAAOC,MAAQ,YAAYqC,GACzB,OAAOD,EAAKtC,cAAc8C,MAAMnD,KAAM4C,GACnCU,KAAKC,IACJ,IAAKA,EAASC,GAAI,CAChB,MAAMlC,EAAY,CAChBC,KAAMjD,EACNO,QAAS,gBAAgB0E,EAASE,UAAUF,EAASG,aACrD7B,IAAKe,EAAK,GACVa,OAAQF,EAASE,OACjBC,WAAYH,EAASG,WACrB9B,UAAWpD,IACXmF,QAASrD,OAAOwB,SAASC,KACzBC,UAAWC,UAAUD,UACrBE,SAAUqB,EAASE,QAAU,IAAMpF,EAAsBA,GAG3DsE,EAAK5C,OAAOoC,SAASb,EACvB,CACA,OAAOiC,IAERK,MAAMhF,IACL,MAAM0C,EAAY,CAChBC,KAAMjD,EACNO,QAAS,gBAAgBD,EAAMC,UAC/BgD,IAAKe,EAAK,GACVhE,MAAOA,EAAMyD,WACbT,UAAWpD,IACXmF,QAASrD,OAAOwB,SAASC,KACzBC,UAAWC,UAAUD,UACrBE,SAAU7D,GAIZ,MADAsE,EAAK5C,OAAOoC,SAASb,GACf1C,GAEZ,CACF,CAKA,oBAAAyE,GACE,MAAMV,EAAO3C,KAEbS,eAAeC,UAAUC,KAAO,SAASkD,EAAQhC,KAAQe,GAGvD,OAFA5C,KAAK8D,WAAaD,EAClB7D,KAAK+D,QAAUlC,EACRc,EAAKnC,gBAAgB2C,MAAMnD,KAAM,CAAC6D,EAAQhC,KAAQe,GAC3D,EAEAnC,eAAeC,UAAUG,KAAO,YAAY+B,GAC1C,MAAMoB,EAAMhE,KAoCZ,OAlCAgE,EAAI5C,iBAAiB,QAAS,WAC5B,MAAME,EAAY,CAChBC,KAAMjD,EACNO,QAAS,cAAcmF,EAAIF,cAAcE,EAAID,UAC7CF,OAAQG,EAAIF,WACZjC,IAAKmC,EAAID,QACTnC,UAAWpD,IACXmF,QAASrD,OAAOwB,SAASC,KACzBC,UAAWC,UAAUD,UACrBE,SAAU7D,GAGZsE,EAAK5C,OAAOoC,SAASb,EACvB,GAEA0C,EAAI5C,iBAAiB,OAAQ,WAC3B,GAAI4C,EAAIP,QAAU,IAAK,CACrB,MAAMnC,EAAY,CAChBC,KAAMjD,EACNO,QAAS,cAAcmF,EAAIP,UAAUO,EAAIN,aACzCG,OAAQG,EAAIF,WACZjC,IAAKmC,EAAID,QACTN,OAAQO,EAAIP,OACZC,WAAYM,EAAIN,WAChB9B,UAAWpD,IACXmF,QAASrD,OAAOwB,SAASC,KACzBC,UAAWC,UAAUD,UACrBE,SAAU8B,EAAIP,QAAU,IAAMpF,EAAsBA,GAGtDsE,EAAK5C,OAAOoC,SAASb,EACvB,CACF,GAEOqB,EAAK/B,gBAAgBuC,MAAMnD,KAAM4C,EAC1C,CACF,CAKA,cAAAqB,CAAepF,EAASqF,EAAU,IAChC,MAAM5C,EAAY,CAChBC,KAAMjD,EACNO,aACGqF,EACHtC,UAAWpD,IACXqD,IAAKvB,OAAOwB,SAASC,KACrBC,UAAWC,UAAUD,UACrBE,SAAUgC,EAAQhC,UAAY7D,GAGhC2B,KAAKD,OAAOoC,SAASb,EACvB,CAKA,OAAA6C,GAEEjE,QAAQtB,MAAQoB,KAAKC,qBACrBC,QAAQE,KAAOJ,KAAKG,oBACpBG,OAAOC,MAAQP,KAAKK,cACpBI,eAAeC,UAAUC,KAAOX,KAAKQ,gBACrCC,eAAeC,UAAUG,KAAOb,KAAKY,eACvC,ECrRK,MAAMwD,EACX,WAAAvE,CAAYC,EAAQuE,GAClBrE,KAAKF,OAASA,EACdE,KAAKqE,UAAYA,EACjBrE,KAAKsE,WAAa,GAClBtE,KAAKuE,WAAa,EAClBvE,KAAKwE,cAAgB,KACrBxE,KAAKyE,WAAa,GAGlBzE,KAAK0E,cFgHF,SAAkBC,EAAMC,GAC7B,IAAIC,EACJ,OAAO,YAA6BjC,GAKlCkC,aAAaD,GACbA,EAAUE,WALI,KACZD,aAAaD,GACbF,KAAQ/B,IAGkBgC,EAC9B,CACF,CE1HyBI,CAAS,IAAMhF,KAAKiF,YAAajF,KAAKF,OAAOrC,cAGlEuC,KAAKkF,cAAgBC,YAAY,KAC3BnF,KAAKsE,WAAWc,OAAS,GAC3BpF,KAAKiF,aAENjF,KAAKF,OAAOrC,aACjB,CAKA,QAAA0E,CAASb,GAEP,IAAKtB,KAAKF,OAAO3C,cAAe,OAGhC,GFoBG,SAA2ByB,EAAOkB,GAEvC,GAAIA,EAAO/B,YAAYqH,OAAS,EAAG,CACjC,MAAMC,EAAa/E,OAAOwB,SAASC,KACnC,GAAIjC,EAAO/B,YAAYuH,KAAKC,GACH,iBAAZA,EACFF,EAAWvG,SAASyG,GAEzBA,aAAmBC,QACdD,EAAQE,KAAKJ,IAItB,OAAO,CAEX,CAGA,SAAIvF,EAAO9B,gBAAgBoH,OAAS,GAAKxG,EAAMC,SACzCiB,EAAO9B,gBAAgBsH,KAAKC,GACP,iBAAZA,EACF3G,EAAMC,QAAQC,SAASyG,GAE5BA,aAAmBC,QACdD,EAAQE,KAAK7G,EAAMC,UASlC,CErDQ6G,CAAkBpE,EAAWtB,KAAKF,QAAS,OAG/C,GAAIE,KAAKuE,YAAcvE,KAAKF,OAAOvC,oBAIjC,YAHIyC,KAAKF,OAAO3B,OACd+B,QAAQE,KAAK,yCAMjB,MAGMuF,EAAgB,IF3BnB,SAA2BC,EAAM9F,GACtC,IAAKA,EAAOhC,aAAc,OAAO8H,EAEjC,MAAMC,EAAY,IAAKD,GASvB,GANIC,EAAUlE,QACZkE,EAAUlE,MAAQkE,EAAUlE,MAAMmE,QAAQ,mBAAoB,cAC9DD,EAAUlE,MAAQkE,EAAUlE,MAAMmE,QAAQ,kBAAmB,cAI3DD,EAAUhE,IACZ,IACE,MAAMA,EAAM,IAAIkE,IAAIF,EAAUhE,KACN,CAAC,QAAS,MAAO,WAAY,SAAU,QAC/CtC,QAAQyG,IAClBnE,EAAIoE,aAAaC,IAAIF,IACvBnE,EAAIoE,aAAaE,IAAIH,EAAO,SAGhCH,EAAUhE,IAAMA,EAAIQ,UACtB,CAAE,MAAO+D,GAET,CAGF,OAAOP,CACT,CEJ2BQ,CAAkB/E,EAAWtB,KAAKF,QAKvDuE,UAAWrE,KAAKqE,UAChBiC,QAAStG,KAAKuG,kBACdrJ,YAAa8C,KAAKF,OAAO5C,YACzBsJ,SAAU,CACRC,MAAOnG,OAAOoG,WACdC,OAAQrG,OAAOsG,aAEjBC,OAAQ,CACNJ,MAAOnG,OAAOuG,OAAOJ,MACrBE,OAAQrG,OAAOuG,OAAOF,SAK1B3G,KAAKsE,WAAWwC,KAAKnB,GACrB3F,KAAKuE,aACLvE,KAAKwE,cAAgB/F,KAAKsI,MAEtB/G,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,oBAAqBrB,GAI/B3F,KAAKsE,WAAWc,QAAUpF,KAAKF,OAAOtC,WACjB,aAAvB8D,EAAUY,SACVlC,KAAKiF,YAGLjF,KAAK0E,eAET,CAKA,eAAMO,GACJ,GAA+B,IAA3BjF,KAAKsE,WAAWc,OAAc,OAElC,MAAM6B,EAAQ,IAAIjH,KAAKsE,YACvBtE,KAAKsE,WAAa,GAElB,MAAM4C,EAAU,CACdC,OAAQF,EACR5C,UAAWrE,KAAKqE,UAChBtH,UAAWiD,KAAKF,OAAO/C,UACvB6E,WAAW,IAAInD,MAAOC,cACtB0I,QAASpH,KAAKqH,mBAGhB,UACQrH,KAAKsH,aAAaJ,GAEpBlH,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,+BAAgCE,EAEhD,CAAE,MAAOtI,GACHoB,KAAKF,OAAO3B,OACd+B,QAAQtB,MAAM,4BAA6BA,GAI7CoB,KAAKyE,WAAWqC,KAAK,CACnBI,UACAK,SAAU,EACV3F,UAAWnD,KAAKsI,QAGlB/G,KAAKwH,mBACP,CACF,CAKA,kBAAMF,CAAaJ,GACjB,MAAMrF,EAAM,GAAG7B,KAAKF,OAAOhD,UAAUkD,KAAKF,OAAOjD,cAE3C4K,EAAU,CACd,eAAgB,oBAGdzH,KAAKF,OAAOjC,SACd4J,EAAuB,cAAI,UAAUzH,KAAKF,OAAOjC,UAGnD,MAAM0F,QAAiBhD,MAAMsB,EAAK,CAChCgC,OAAQ,OACR4D,UACAC,KAAM3E,KAAKC,UAAUkE,GACrBS,KAAM,SAGR,IAAKpE,EAASC,GACZ,MAAM,IAAIoE,MAAM,QAAQrE,EAASE,WAAWF,EAASG,cAGvD,OAAOH,EAASsE,MAClB,CAKA,uBAAML,GACJ,MAAMT,EAAMtI,KAAKsI,MACXe,EAAe,GAErB9H,KAAKyE,WAAazE,KAAKyE,WAAWsD,OAAOtI,IACvC,GAAIA,EAAK8H,UAAYvH,KAAKF,OAAO7B,WAI/B,OAHI+B,KAAKF,OAAO3B,OACd+B,QAAQE,KAAK,sCAAuCX,EAAKyH,QAAQE,UAE5D,EAMT,QAH6BL,EAAMtH,EAAKmC,WACrB5B,KAAKF,OAAO5B,WAAa8J,KAAKC,IAAI,EAAGxI,EAAK8H,aAG3DO,EAAahB,KAAKrH,IACX,KAOX,IAAK,MAAMA,KAAQqI,EACjB,UACQ9H,KAAKsH,aAAa7H,EAAKyH,SAEzBlH,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,kCAAmCvH,EAAKyH,QAAQE,QAEhE,CAAE,MAAOxI,GACHoB,KAAKF,OAAO3B,OACd+B,QAAQtB,MAAM,8BAA+Ba,EAAKyH,QAAQE,QAASxI,GAIrEoB,KAAKyE,WAAWqC,KAAK,IAChBrH,EACH8H,SAAU9H,EAAK8H,SAAW,EAC1B3F,UAAWmF,GAEf,CAIE/G,KAAKyE,WAAWW,OAAS,GAC3BL,WAAW,IAAM/E,KAAKwH,oBAAqBxH,KAAKF,OAAO5B,WAE3D,CAKA,eAAAqI,GACE,MAAO,OAAS9H,KAAKsI,MAAQ,IAAMiB,KAAKE,SAAS7F,SAAS,IAAI8F,OAAO,EAAG,EAC1E,CAKA,eAAAd,GACE,MAAO,SAAW5I,KAAKsI,MAAQ,IAAMiB,KAAKE,SAAS7F,SAAS,IAAI8F,OAAO,EAAG,EAC5E,CAKA,QAAAC,GACE,MAAO,CACLC,YAAarI,KAAKuE,WAClB+D,aAActI,KAAKsE,WAAWc,OAC9BmD,eAAgBvI,KAAKyE,WAAWW,OAChCZ,cAAexE,KAAKwE,cACpBH,UAAWrE,KAAKqE,UAEpB,CAKA,UAAAmE,GACExI,KAAKsE,WAAa,GAClBtE,KAAKyE,WAAa,EACpB,CAKA,OAAAN,GAEMnE,KAAKsE,WAAWc,OAAS,GAC3BpF,KAAKiF,YAIHjF,KAAKkF,eACPuD,cAAczI,KAAKkF,eAIrBlF,KAAKwI,YACP,ECxPK,MAAME,EACX,WAAA7I,CAAYC,GACVE,KAAKF,OAASA,EACdE,KAAK2I,eAAiB,KACtB3I,KAAK4I,aAAe,IAAIC,IACxB7I,KAAK8I,aAAe,EAEpB9I,KAAK+I,uBACL/I,KAAKgJ,cACP,CAKA,oBAAAD,GACO/I,KAAKF,OAAOpC,iBAEjBsC,KAAK2I,eAAiBM,SAASC,cAAc,OAC7ClJ,KAAK2I,eAAeQ,GAAK,sBACzBnJ,KAAK2I,eAAeS,UAAY,yBAAyBpJ,KAAKF,OAAOnC,gBAErEsL,SAASvB,KAAK2B,YAAYrJ,KAAK2I,gBACjC,CAKA,YAAAK,GACE,IAAKhJ,KAAKF,OAAOpC,eAAgB,OAEjC,MAkIM4L,EAAaL,SAASC,cAAc,SAC1CI,EAAWC,YAnII,8zFAoIfN,SAASO,KAAKH,YAAYC,EAC5B,CAKA,cAAAG,CAAenI,GACb,IAAKtB,KAAKF,OAAOpC,iBAAmBsC,KAAK2I,eAAgB,OAEzD,MAAMe,EAAY1J,KAAK2J,yBAAyBrI,EAAUY,UACpD0H,IAAY5J,KAAK8I,aAEjBe,EAAQ7J,KAAK8J,YAAY,CAC7BX,GAAIS,EACJrI,KAAMmI,EACNK,MAAO/J,KAAKgK,cAAc1I,GAC1BzC,QAASmB,KAAKiK,gBAAgB3I,GAC9B4I,QAASlK,KAAKmK,gBAAgB7I,KAkBhC,OAfAtB,KAAK2I,eAAeU,YAAYQ,GAChC7J,KAAK4I,aAAazC,IAAIyD,EAASC,GAG/B9E,WAAW,KACT8E,EAAMO,UAAUC,IAAI,sBACnB,IAGC/I,EAAUY,WAAa7D,GACzB0G,WAAW,KACT/E,KAAKsK,UAAUV,IACd5J,KAAKF,OAAOlC,eAGVgM,CACT,CAKA,WAAAE,EAAYX,GAAEA,EAAE5H,KAAEA,EAAIwI,MAAEA,EAAKlL,QAAEA,EAAOqL,QAAEA,IACtC,MAAML,EAAQZ,SAASC,cAAc,OACrCW,EAAMT,UAAY,uBAAuB7H,IACzCsI,EAAMU,QAAQX,QAAUT,EAExB,MAAMqB,EAASvB,SAASC,cAAc,OACtCsB,EAAOpB,UAAY,mBAEnB,MAAMqB,EAAexB,SAASC,cAAc,MAC5CuB,EAAarB,UAAY,kBACzBqB,EAAalB,YAAcQ,EAE3B,MAAMW,EAAczB,SAASC,cAAc,UAC3CwB,EAAYtB,UAAY,kBACxBsB,EAAYC,UAAY,IACxBD,EAAYE,QAAU,IAAM5K,KAAKsK,UAAUnB,GAE3CqB,EAAOnB,YAAYoB,GACnBD,EAAOnB,YAAYqB,GAEnB,MAAMG,EAAiB5B,SAASC,cAAc,OAO9C,GANA2B,EAAezB,UAAY,oBAC3ByB,EAAetB,YAAc1K,EAE7BgL,EAAMR,YAAYmB,GAClBX,EAAMR,YAAYwB,GAEdX,GAAWA,EAAQ9E,OAAS,EAAG,CACjC,MAAM0F,EAAmB7B,SAASC,cAAc,OAChD4B,EAAiB1B,UAAY,oBAE7Bc,EAAQ3K,QAAQwL,IACd,MAAMC,EAAS/B,SAASC,cAAc,UACtC8B,EAAO5B,UAAY,qBAAoB2B,EAAOE,QAAU,2BAA6B,IACrFD,EAAOzB,YAAcwB,EAAOG,KAC5BF,EAAOJ,QAAU,KACfG,EAAOI,WACqB,IAAxBJ,EAAOK,cACTpL,KAAKsK,UAAUnB,IAGnB2B,EAAiBzB,YAAY2B,KAG/BnB,EAAMR,YAAYyB,EACpB,CAEA,OAAOjB,CACT,CAKA,SAAAS,CAAUV,GACR,MAAMC,EAAQ7J,KAAK4I,aAAayC,IAAIzB,GAC/BC,IAELA,EAAMO,UAAUkB,OAAO,qBAEvBvG,WAAW,KACL8E,EAAM0B,YACR1B,EAAM0B,WAAWC,YAAY3B,GAE/B7J,KAAK4I,aAAa6C,OAAO7B,IACxB,KACL,CAKA,wBAAAD,CAAyBzH,GACvB,OAAQA,GACN,KAAK7D,EAEL,KAAKA,EACH,OAAOE,EACT,KAAKF,EACH,OAAOE,EAGT,QACE,OAAOA,EAEb,CAKA,aAAAyL,CAAc1I,GACZ,OAAQA,EAAUY,UAChB,KAAK7D,EACH,MAAO,0BACT,KAAKA,EACH,MAAO,iBACT,KAAKA,EACH,MAAO,UACT,KAAKA,EACH,MAAO,SACT,QACE,MAAO,QAEb,CAKA,eAAA4L,CAAgB3I,GACd,OAAQA,EAAUC,MAChB,IAAK,aACH,MAAO,6DACT,IAAK,UACH,MAAO,0DACT,IAAK,WACH,MAAO,sCACT,QACE,MAAO,gCAEb,CAKA,eAAA4I,CAAgB7I,GACd,MAAM4I,EAAU,GAoChB,OAjCI5I,EAAUY,WAAa7D,GACzB6L,EAAQpD,KAAK,CACXoE,KAAM,cACND,SAAS,EACTE,QAAS,IAAM7K,OAAOwB,SAAS4J,WAKZ,YAAnBpK,EAAUC,MACZ2I,EAAQpD,KAAK,CACXoE,KAAM,QACND,SAAS,EACTE,QAAS,KAEP7K,OAAOqL,cAAc,IAAIC,YAAY,YAAa,CAChDC,OAAQ,CAAEvK,mBAOlB4I,EAAQpD,KAAK,CACXoE,KAAM,kBACNC,QAAS,KAEP7K,OAAOqL,cAAc,IAAIC,YAAY,sBAAuB,CAC1DC,OAAQ,CAAEvK,mBAKT4I,CACT,CAKA,WAAA4B,CAAYjN,GACV,IAAKmB,KAAKF,OAAOpC,iBAAmBsC,KAAK2I,eAAgB,OAEzD,MAAMiB,IAAY5J,KAAK8I,aACjBe,EAAQ7J,KAAK8J,YAAY,CAC7BX,GAAIS,EACJrI,KAAMhD,EACNwL,MAAO,UACPlL,UACAqL,QAAS,KAcX,OAXAlK,KAAK2I,eAAeU,YAAYQ,GAChC7J,KAAK4I,aAAazC,IAAIyD,EAASC,GAE/B9E,WAAW,KACT8E,EAAMO,UAAUC,IAAI,sBACnB,IAEHtF,WAAW,KACT/E,KAAKsK,UAAUV,IACd,KAEIA,CACT,CAKA,cAAAmC,GACE/L,KAAK4I,aAAarJ,QAAQ,CAACsK,EAAOV,KAChCnJ,KAAKsK,UAAUnB,IAEnB,CAKA,OAAAhF,GACEnE,KAAK+L,iBAED/L,KAAK2I,gBAAkB3I,KAAK2I,eAAe4C,YAC7CvL,KAAK2I,eAAe4C,WAAWC,YAAYxL,KAAK2I,eAEpD,ECxZK,MAAMqD,EACX,WAAAnM,CAAYoM,EAAa,IAEvBjM,KAAKF,OAASf,EAAUnC,EAAgBqP,GAGxCjM,KAAKqE,UJRA,OAAS5F,KAAKsI,MAAQ,IAAMiB,KAAKE,SAAS7F,SAAS,IAAI8F,OAAO,EAAG,GIWtEnI,KAAKD,OAAS,KACdC,KAAKkM,aAAe,KACpBlM,KAAKmM,GAAK,KAGVnM,KAAKoM,YAAc,IAAIvD,IACvB7I,KAAKqM,cAAgB,EAGrBrM,KAAKsM,eJmHF,SAAkB3H,EAAM4H,GAC7B,IAAIC,EACJ,OAAO,WACL,MAAM5J,EAAO6J,UACPC,EAAU1M,KACXwM,IACH7H,EAAKxB,MAAMuJ,EAAS9J,GACpB4J,GAAa,EACbzH,WAAW,IAAMyH,GAAa,EAAOD,GAEzC,CACF,CI9H0BI,CAAUrL,IAC9BtB,KAAK4M,oBAAoBtL,IACxB,KAGyB,YAAxB2H,SAAS4D,WACX5D,SAAS7H,iBAAiB,mBAAoB,IAAMpB,KAAK8M,cAEzD9M,KAAK8M,YAET,CAKA,UAAAA,GACE,IACM9M,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,gCAAiChH,KAAKF,QAIpDE,KAAKD,OAAS,IAAIqE,EAAYpE,KAAKF,OAAQE,KAAKqE,WAGhDrE,KAAKmM,GAAK,IAAIzD,EAAc1I,KAAKF,QAGjCE,KAAKkM,aAAe,IAAItM,EAAaI,KAAKF,OAAQ,CAChDqC,SAAWb,GAActB,KAAK+M,YAAYzL,KAI5CtB,KAAKgN,6BAGLhN,KAAKiN,2BAGLjN,KAAKkN,4BAEDlN,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,iCAIdhH,KAAKmN,qBAEP,CAAE,MAAOvO,GACPsB,QAAQtB,MAAM,4BAA6BA,EAC7C,CACF,CAKA,WAAAmO,CAAYzL,GAEVtB,KAAKD,OAAOoC,SAASb,GAGrBtB,KAAKoN,oBAAoB9L,GAGrBA,EAAUY,WAAa7D,GACvBiD,EAAUY,WAAa7D,GACzB2B,KAAKmM,GAAG1C,eAAenI,GAIrBtB,KAAKqN,sBACPrN,KAAKsM,eAAehL,EAExB,CAKA,mBAAA8L,CAAoB9L,GAClB,MAAMyF,EAAMtI,KAAKsI,MACX9J,EAAa+C,KAAKF,OAAO7C,WAG/B,IAAK,MAAO2E,EAAW0L,KAAUtN,KAAKoM,YAAYmB,UAC5CxG,EAAMnF,EAAY3E,GACpB+C,KAAKoM,YAAYX,OAAO7J,GAK5B,MAAM4L,EAAcxF,KAAKyF,MAAM1G,EAAM9J,GAAcA,EAC7CyQ,EAAe1N,KAAKoM,YAAYf,IAAImC,IAAgB,EAC1DxN,KAAKoM,YAAYjG,IAAIqH,EAAaE,EAAe,EACnD,CAKA,kBAAAL,GAIE,OAHoB3N,MAAMiO,KAAK3N,KAAKoM,YAAYwB,UAC7CC,OAAO,CAACC,EAAKR,IAAUQ,EAAMR,EAAO,IAEjBtN,KAAKF,OAAO9C,cACpC,CAKA,mBAAA4P,CAAoBtL,GAClB,MAAMyF,EAAMtI,KAAKsI,MAGbA,EAAM/G,KAAKqM,cAAgB,MAI/BrM,KAAKqM,cAAgBtF,EAGrBzG,OAAOqL,cAAc,IAAIC,YAAY,qBAAsB,CACzDC,OAAQ,CACNvK,YACA+C,UAAWrE,KAAKqE,UAChBE,WAAY7E,MAAMiO,KAAK3N,KAAKoM,YAAYwB,UACrCC,OAAO,CAACC,EAAKR,IAAUQ,EAAMR,EAAO,OAIvCtN,KAAKF,OAAO3B,OACd+B,QAAQE,KAAK,yCAA0CkB,GAE3D,CAKA,0BAAA0L,GACE/D,SAAS7H,iBAAiB,mBAAoB,KACxC6H,SAAS8E,QAEX/N,KAAKD,OAAOkF,aAGlB,CAKA,wBAAAgI,GACE3M,OAAOc,iBAAiB,eAAgB,KAEtCpB,KAAKD,OAAOkF,aAEhB,CAKA,yBAAAiI,GAEE5M,OAAOc,iBAAiB,YAAcC,IACpC,MAAMC,UAAEA,GAAcD,EAAMwK,OAExB7L,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,iCAAkC1F,GAIhDyD,WAAW,KACT/E,KAAKmM,GAAGL,YAAY,iCACnB,OAILxL,OAAOc,iBAAiB,sBAAwBC,IAC9C,MAAMC,UAAEA,GAAcD,EAAMwK,OAExB7L,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,2CAA4C1F,GAI1D,MAAM0M,EAAa,kEAAkE1M,EAAUgF,yBAAyBtG,KAAKqE,sBAAsB4J,mBAAmB3M,EAAUzC,WAChLyB,OAAOK,KAAKqN,IAEhB,CAKA,mBAAAb,GAEE7M,OAAO4N,IAAM,CAEX/L,SAAU,CAACtD,EAASqF,EAAU,MAC5BlE,KAAKkM,aAAajI,eAAepF,EAASqF,IAI5CkE,SAAU,KACD,IACFpI,KAAKD,OAAOqI,WACftI,OAAQE,KAAKF,OACbqO,eAAgBhP,OAAOiP,YAAYpO,KAAKoM,eAK5CiC,aAAeC,IACbtO,KAAKF,OAASf,EAAUiB,KAAKF,OAAQwO,GACjCtO,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,6BAA8BhH,KAAKF,SAKnDyO,YAAa,KACXvO,KAAKD,OAAOyI,aACZxI,KAAKoM,YAAYoC,QACjBxO,KAAKmM,GAAGJ,kBAIV5H,QAAS,KACPnE,KAAKmE,WAGX,CAKA,QAAAiE,GACE,MAAO,CACL/D,UAAWrE,KAAKqE,UAChBvE,OAAQE,KAAKF,OACbC,OAAQC,KAAKD,OAASC,KAAKD,OAAOqI,WAAa,KAC/C+F,eAAgBhP,OAAOiP,YAAYpO,KAAKoM,aACxCC,cAAerM,KAAKqM,cAExB,CAKA,YAAAgC,CAAaC,GACXtO,KAAKF,OAASf,EAAUiB,KAAKF,OAAQwO,GAEjCtO,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,6BAA8BhH,KAAKF,OAEnD,CAKA,OAAAqE,GACMnE,KAAKF,OAAO3B,OACd+B,QAAQ8G,IAAI,4BAIVhH,KAAKkM,cACPlM,KAAKkM,aAAa/H,UAGhBnE,KAAKD,QACPC,KAAKD,OAAOoE,UAGVnE,KAAKmM,IACPnM,KAAKmM,GAAGhI,UAIVnE,KAAKoM,YAAYoC,QAGblO,OAAO4N,YACF5N,OAAO4N,GAElB,EAIFjF,SAAS7H,iBAAiB,mBAAoB,KAC5C,MAAMqN,EAAYxF,SAASyF,cAAc,2BACzC,GAAID,EACF,IACE,MAAM3O,EAASiD,KAAK4L,MAAMF,EAAUlE,QAAQqE,WAC5C,IAAI5C,EAAUlM,EAChB,CAAE,MAAOlB,GACPsB,QAAQtB,MAAM,8CAA+CA,EAC/D"}