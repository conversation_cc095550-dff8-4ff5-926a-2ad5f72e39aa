(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.RUMScript = {}));
})(this, (function (exports) { 'use strict';

  /**
   * Default configuration for RUM Script
   */
  const DEFAULT_CONFIG = {
    // Server endpoints
    logEndpoint: '/api/errors',
    baseUrl: 'http://localhost:3001',

    // Project settings
    projectId: 'demo_project_001',

    // Error thresholds
    errorThreshold: 5,
    timeWindow: 5 * 60 * 1000, // 5 minutes in milliseconds

    // Environment settings
    environment: 'production',
    enableLogging: true,
    enableConsoleCapture: true,
    enableNetworkCapture: true,
    enableResourceCapture: true,

    // Performance settings
    maxErrorsPerSession: 100,
    batchSize: 10,
    batchTimeout: 5000, // 5 seconds

    // User interface settings
    showUserAlerts: true,
    alertPosition: 'top-right',
    alertDuration: 5000,

    // Security settings
    apiKey: 'demo_api_key_12345',
    sanitizeData: true,
    excludeUrls: [],
    excludeMessages: [],

    // Retry settings
    maxRetries: 3,
    retryDelay: 1000,

    // Debug settings
    debug: false,
    verbose: false
  };

  /**
   * Error severity levels
   */
  const ERROR_SEVERITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
  };

  /**
   * Error types
   */
  const ERROR_TYPES = {
    JAVASCRIPT: 'javascript',
    NETWORK: 'network',
    RESOURCE: 'resource',
    CONSOLE: 'console',
    UNHANDLED_REJECTION: 'unhandled_rejection',
    CUSTOM: 'custom'
  };

  /**
   * Alert types
   */
  const ALERT_TYPES = {
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info',
    SUCCESS: 'success'
  };

  /**
   * Utility functions for RUM Script
   */

  /**
   * Generate a unique session ID
   */
  function generateSessionId() {
    return 'rum_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get current timestamp in ISO format
   */
  function getCurrentTimestamp() {
    return new Date().toISOString();
  }

  /**
   * Sanitize error data to remove sensitive information
   */
  function sanitizeErrorData(data, config) {
    if (!config.sanitizeData) return data;

    const sanitized = { ...data };

    // Remove potential sensitive data from stack traces
    if (sanitized.stack) {
      sanitized.stack = sanitized.stack.replace(/\/Users\/<USER>\/]+/g, '/Users/<USER>');
      sanitized.stack = sanitized.stack.replace(/\/home\/<USER>\/]+/g, '/home/<USER>');
    }

    // Remove sensitive URL parameters
    if (sanitized.url) {
      try {
        const url = new URL(sanitized.url);
        const sensitiveParams = ['token', 'key', 'password', 'secret', 'auth'];
        sensitiveParams.forEach(param => {
          if (url.searchParams.has(param)) {
            url.searchParams.set(param, '***');
          }
        });
        sanitized.url = url.toString();
      } catch (e) {
        // Invalid URL, keep as is
      }
    }

    return sanitized;
  }

  /**
   * Check if error should be ignored based on configuration
   */
  function shouldIgnoreError(error, config) {
    // Check excluded URLs
    if (config.excludeUrls.length > 0) {
      const currentUrl = window.location.href;
      if (config.excludeUrls.some(pattern => {
        if (typeof pattern === 'string') {
          return currentUrl.includes(pattern);
        }
        if (pattern instanceof RegExp) {
          return pattern.test(currentUrl);
        }
        return false;
      })) {
        return true;
      }
    }

    // Check excluded messages
    if (config.excludeMessages.length > 0 && error.message) {
      if (config.excludeMessages.some(pattern => {
        if (typeof pattern === 'string') {
          return error.message.includes(pattern);
        }
        if (pattern instanceof RegExp) {
          return pattern.test(error.message);
        }
        return false;
      })) {
        return true;
      }
    }

    return false;
  }

  /**
   * Determine error severity based on error details
   */
  function determineErrorSeverity(error) {
    // Import here to avoid circular dependency
    const ERROR_SEVERITY = {
      LOW: 'low',
      CRITICAL: 'critical'
    };

    // Critical errors that break functionality
    if (error.message && (
      error.message.includes('ChunkLoadError') ||
      error.message.includes('Loading chunk') ||
      error.message.includes('Cannot read property') ||
      error.message.includes('is not a function')
    )) {
      return ERROR_SEVERITY.CRITICAL;
    }

    // Default to low severity
    return ERROR_SEVERITY.LOW;
  }

  /**
   * Debounce function to limit function calls
   */
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  /**
   * Throttle function to limit function calls
   */
  function throttle(func, limit) {
    let inThrottle;
    return function () {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  /**
   * Deep merge objects
   */
  function deepMerge(target, source) {
    const output = Object.assign({}, target);
    if (isObject(target) && isObject(source)) {
      Object.keys(source).forEach(key => {
        if (isObject(source[key])) {
          if (!(key in target))
            Object.assign(output, { [key]: source[key] });
          else
            output[key] = deepMerge(target[key], source[key]);
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    return output;
  }

  function isObject(item) {
    return item && typeof item === 'object' && !Array.isArray(item);
  }

  /**
   * Error capture functionality for RUM Script
   */


  class ErrorCapture {
    constructor(config, logger) {
      this.config = config;
      this.logger = logger;
      this.originalConsoleError = console.error;
      this.originalConsoleWarn = console.warn;
      this.originalFetch = window.fetch;
      this.originalXHROpen = XMLHttpRequest.prototype.open;
      this.originalXHRSend = XMLHttpRequest.prototype.send;
      
      this.setupErrorHandlers();
    }

    /**
     * Set up all error handlers
     */
    setupErrorHandlers() {
      this.setupJavaScriptErrorHandler();
      this.setupUnhandledRejectionHandler();
      this.setupResourceErrorHandler();
      
      if (this.config.enableConsoleCapture) {
        this.setupConsoleErrorHandler();
      }
      
      if (this.config.enableNetworkCapture) {
        this.setupNetworkErrorHandler();
      }
    }

    /**
     * Handle JavaScript errors
     */
    setupJavaScriptErrorHandler() {
      window.addEventListener('error', (event) => {
        const errorData = {
          type: ERROR_TYPES.JAVASCRIPT,
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error ? event.error.stack : null,
          timestamp: getCurrentTimestamp(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          severity: determineErrorSeverity({
            message: event.message})
        };
        
        this.logger.logError(errorData);
      });
    }

    /**
     * Handle unhandled promise rejections
     */
    setupUnhandledRejectionHandler() {
      window.addEventListener('unhandledrejection', (event) => {
        const errorData = {
          type: ERROR_TYPES.UNHANDLED_REJECTION,
          message: event.reason ? event.reason.toString() : 'Unhandled Promise Rejection',
          stack: event.reason && event.reason.stack ? event.reason.stack : null,
          timestamp: getCurrentTimestamp(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          severity: ERROR_SEVERITY.HIGH
        };
        
        this.logger.logError(errorData);
      });
    }

    /**
     * Handle resource loading errors
     */
    setupResourceErrorHandler() {
      if (!this.config.enableResourceCapture) return;
      
      window.addEventListener('error', (event) => {
        // Only handle resource errors (not JavaScript errors)
        if (event.target !== window && event.target.tagName) {
          const errorData = {
            type: ERROR_TYPES.RESOURCE,
            message: `Failed to load ${event.target.tagName.toLowerCase()}: ${event.target.src || event.target.href}`,
            resourceType: event.target.tagName.toLowerCase(),
            resourceUrl: event.target.src || event.target.href,
            timestamp: getCurrentTimestamp(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            severity: ERROR_SEVERITY.MEDIUM
          };
          
          this.logger.logError(errorData);
        }
      }, true); // Use capture phase
    }

    /**
     * Handle console errors
     */
    setupConsoleErrorHandler() {
      const self = this;
      
      console.error = function(...args) {
        const errorData = {
          type: ERROR_TYPES.CONSOLE,
          message: args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
          ).join(' '),
          timestamp: getCurrentTimestamp(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          severity: ERROR_SEVERITY.MEDIUM
        };
        
        self.logger.logError(errorData);
        
        // Call original console.error
        self.originalConsoleError.apply(console, args);
      };
      
      console.warn = function(...args) {
        if (self.config.verbose) {
          const errorData = {
            type: ERROR_TYPES.CONSOLE,
            message: '[WARN] ' + args.map(arg => 
              typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
            ).join(' '),
            timestamp: getCurrentTimestamp(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            severity: ERROR_SEVERITY.LOW
          };
          
          self.logger.logError(errorData);
        }
        
        // Call original console.warn
        self.originalConsoleWarn.apply(console, args);
      };
    }

    /**
     * Handle network errors
     */
    setupNetworkErrorHandler() {
      this.setupFetchErrorHandler();
      this.setupXHRErrorHandler();
    }

    /**
     * Handle fetch API errors
     */
    setupFetchErrorHandler() {
      const self = this;
      
      window.fetch = function(...args) {
        return self.originalFetch.apply(this, args)
          .then(response => {
            if (!response.ok) {
              const errorData = {
                type: ERROR_TYPES.NETWORK,
                message: `Fetch error: ${response.status} ${response.statusText}`,
                url: args[0],
                status: response.status,
                statusText: response.statusText,
                timestamp: getCurrentTimestamp(),
                pageUrl: window.location.href,
                userAgent: navigator.userAgent,
                severity: response.status >= 500 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM
              };
              
              self.logger.logError(errorData);
            }
            return response;
          })
          .catch(error => {
            const errorData = {
              type: ERROR_TYPES.NETWORK,
              message: `Fetch error: ${error.message}`,
              url: args[0],
              error: error.toString(),
              timestamp: getCurrentTimestamp(),
              pageUrl: window.location.href,
              userAgent: navigator.userAgent,
              severity: ERROR_SEVERITY.HIGH
            };
            
            self.logger.logError(errorData);
            throw error;
          });
      };
    }

    /**
     * Handle XMLHttpRequest errors
     */
    setupXHRErrorHandler() {
      const self = this;
      
      XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._rumMethod = method;
        this._rumUrl = url;
        return self.originalXHROpen.apply(this, [method, url, ...args]);
      };
      
      XMLHttpRequest.prototype.send = function(...args) {
        const xhr = this;
        
        xhr.addEventListener('error', function() {
          const errorData = {
            type: ERROR_TYPES.NETWORK,
            message: `XHR error: ${xhr._rumMethod} ${xhr._rumUrl}`,
            method: xhr._rumMethod,
            url: xhr._rumUrl,
            timestamp: getCurrentTimestamp(),
            pageUrl: window.location.href,
            userAgent: navigator.userAgent,
            severity: ERROR_SEVERITY.HIGH
          };
          
          self.logger.logError(errorData);
        });
        
        xhr.addEventListener('load', function() {
          if (xhr.status >= 400) {
            const errorData = {
              type: ERROR_TYPES.NETWORK,
              message: `XHR error: ${xhr.status} ${xhr.statusText}`,
              method: xhr._rumMethod,
              url: xhr._rumUrl,
              status: xhr.status,
              statusText: xhr.statusText,
              timestamp: getCurrentTimestamp(),
              pageUrl: window.location.href,
              userAgent: navigator.userAgent,
              severity: xhr.status >= 500 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM
            };
            
            self.logger.logError(errorData);
          }
        });
        
        return self.originalXHRSend.apply(this, args);
      };
    }

    /**
     * Manually log a custom error
     */
    logCustomError(message, details = {}) {
      const errorData = {
        type: ERROR_TYPES.CUSTOM,
        message,
        ...details,
        timestamp: getCurrentTimestamp(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        severity: details.severity || ERROR_SEVERITY.MEDIUM
      };
      
      this.logger.logError(errorData);
    }

    /**
     * Clean up error handlers
     */
    destroy() {
      // Restore original functions
      console.error = this.originalConsoleError;
      console.warn = this.originalConsoleWarn;
      window.fetch = this.originalFetch;
      XMLHttpRequest.prototype.open = this.originalXHROpen;
      XMLHttpRequest.prototype.send = this.originalXHRSend;
    }
  }

  /**
   * Error logging functionality for RUM Script
   */


  class ErrorLogger {
    constructor(config, sessionId) {
      this.config = config;
      this.sessionId = sessionId;
      this.errorQueue = [];
      this.errorCount = 0;
      this.lastErrorTime = null;
      this.retryQueue = [];

      // Debounced batch send function
      this.debouncedSend = debounce(() => this.sendBatch(), this.config.batchTimeout);

      // Set up periodic batch sending
      this.batchInterval = setInterval(() => {
        if (this.errorQueue.length > 0) {
          this.sendBatch();
        }
      }, this.config.batchTimeout);
    }

    /**
     * Log an error
     */
    logError(errorData) {
      // Check if logging is enabled
      if (!this.config.enableLogging) return;

      // Check if error should be ignored
      if (shouldIgnoreError(errorData, this.config)) return;

      // Check if we've exceeded max errors per session
      if (this.errorCount >= this.config.maxErrorsPerSession) {
        if (this.config.debug) {
          console.warn('RUM: Max errors per session exceeded');
        }
        return;
      }

      // Sanitize error data
      const sanitizedError = sanitizeErrorData(errorData, this.config);

      // Add session information
      const enrichedError = {
        ...sanitizedError,
        sessionId: this.sessionId,
        errorId: this.generateErrorId(),
        environment: this.config.environment,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        screen: {
          width: window.screen.width,
          height: window.screen.height
        }
      };

      // Add to queue
      this.errorQueue.push(enrichedError);
      this.errorCount++;
      this.lastErrorTime = Date.now();

      if (this.config.debug) {
        console.log('RUM: Error logged', enrichedError);
      }

      // Send immediately if batch size reached or critical error
      if (this.errorQueue.length >= this.config.batchSize ||
        errorData.severity === 'critical') {
        this.sendBatch();
      } else {
        // Otherwise, debounce the send
        this.debouncedSend();
      }
    }

    /**
     * Send batch of errors to server
     */
    async sendBatch() {
      if (this.errorQueue.length === 0) return;

      const batch = [...this.errorQueue];
      this.errorQueue = [];

      const payload = {
        errors: batch,
        sessionId: this.sessionId,
        projectId: this.config.projectId,
        timestamp: new Date().toISOString(),
        batchId: this.generateBatchId()
      };

      try {
        await this.sendToServer(payload);

        if (this.config.debug) {
          console.log('RUM: Batch sent successfully', payload);
        }
      } catch (error) {
        if (this.config.debug) {
          console.error('RUM: Failed to send batch', error);
        }

        // Add to retry queue
        this.retryQueue.push({
          payload,
          attempts: 0,
          timestamp: Date.now()
        });

        this.processRetryQueue();
      }
    }

    /**
     * Send payload to server
     */
    async sendToServer(payload) {
      const url = `${this.config.baseUrl}${this.config.logEndpoint}`;

      const headers = {
        'Content-Type': 'application/json'
      };

      if (this.config.apiKey) {
        headers['Authorization'] = `Bearer ${this.config.apiKey}`;
      }

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
        mode: 'cors'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    }

    /**
     * Process retry queue
     */
    async processRetryQueue() {
      const now = Date.now();
      const itemsToRetry = [];

      this.retryQueue = this.retryQueue.filter(item => {
        if (item.attempts >= this.config.maxRetries) {
          if (this.config.debug) {
            console.warn('RUM: Max retries exceeded for batch', item.payload.batchId);
          }
          return false; // Remove from queue
        }

        const timeSinceLastAttempt = now - item.timestamp;
        const retryDelay = this.config.retryDelay * Math.pow(2, item.attempts); // Exponential backoff

        if (timeSinceLastAttempt >= retryDelay) {
          itemsToRetry.push(item);
          return false; // Remove from queue (will be re-added if retry fails)
        }

        return true; // Keep in queue
      });

      // Process retry items
      for (const item of itemsToRetry) {
        try {
          await this.sendToServer(item.payload);

          if (this.config.debug) {
            console.log('RUM: Retry successful for batch', item.payload.batchId);
          }
        } catch (error) {
          if (this.config.debug) {
            console.error('RUM: Retry failed for batch', item.payload.batchId, error);
          }

          // Add back to retry queue with incremented attempts
          this.retryQueue.push({
            ...item,
            attempts: item.attempts + 1,
            timestamp: now
          });
        }
      }

      // Schedule next retry processing if there are items in queue
      if (this.retryQueue.length > 0) {
        setTimeout(() => this.processRetryQueue(), this.config.retryDelay);
      }
    }

    /**
     * Generate unique error ID
     */
    generateErrorId() {
      return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Generate unique batch ID
     */
    generateBatchId() {
      return 'batch_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Get error statistics
     */
    getStats() {
      return {
        totalErrors: this.errorCount,
        queuedErrors: this.errorQueue.length,
        retryQueueSize: this.retryQueue.length,
        lastErrorTime: this.lastErrorTime,
        sessionId: this.sessionId
      };
    }

    /**
     * Clear error queue
     */
    clearQueue() {
      this.errorQueue = [];
      this.retryQueue = [];
    }

    /**
     * Destroy logger and clean up
     */
    destroy() {
      // Send any remaining errors
      if (this.errorQueue.length > 0) {
        this.sendBatch();
      }

      // Clear intervals
      if (this.batchInterval) {
        clearInterval(this.batchInterval);
      }

      // Clear queues
      this.clearQueue();
    }
  }

  /**
   * User interface for error alerts and recovery options
   */


  class UserInterface {
    constructor(config) {
      this.config = config;
      this.alertContainer = null;
      this.activeAlerts = new Map();
      this.alertCounter = 0;
      
      this.createAlertContainer();
      this.injectStyles();
    }

    /**
     * Create alert container
     */
    createAlertContainer() {
      if (!this.config.showUserAlerts) return;
      
      this.alertContainer = document.createElement('div');
      this.alertContainer.id = 'rum-alert-container';
      this.alertContainer.className = `rum-alerts rum-alerts-${this.config.alertPosition}`;
      
      document.body.appendChild(this.alertContainer);
    }

    /**
     * Inject CSS styles
     */
    injectStyles() {
      if (!this.config.showUserAlerts) return;
      
      const styles = `
      .rum-alerts {
        position: fixed;
        z-index: 10000;
        pointer-events: none;
        max-width: 400px;
      }
      
      .rum-alerts-top-right {
        top: 20px;
        right: 20px;
      }
      
      .rum-alerts-top-left {
        top: 20px;
        left: 20px;
      }
      
      .rum-alerts-bottom-right {
        bottom: 20px;
        right: 20px;
      }
      
      .rum-alerts-bottom-left {
        bottom: 20px;
        left: 20px;
      }
      
      .rum-alert {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin-bottom: 12px;
        padding: 16px;
        pointer-events: auto;
        transform: translateX(100%);
        transition: all 0.3s ease;
        border-left: 4px solid #ccc;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        line-height: 1.4;
      }
      
      .rum-alert.rum-alert-visible {
        transform: translateX(0);
      }
      
      .rum-alert-error {
        border-left-color: #ef4444;
      }
      
      .rum-alert-warning {
        border-left-color: #f59e0b;
      }
      
      .rum-alert-info {
        border-left-color: #3b82f6;
      }
      
      .rum-alert-success {
        border-left-color: #10b981;
      }
      
      .rum-alert-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
      }
      
      .rum-alert-title {
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
      
      .rum-alert-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: #6b7280;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .rum-alert-close:hover {
        color: #374151;
      }
      
      .rum-alert-message {
        color: #4b5563;
        margin-bottom: 12px;
      }
      
      .rum-alert-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }
      
      .rum-alert-button {
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      
      .rum-alert-button:hover {
        background: #e5e7eb;
      }
      
      .rum-alert-button-primary {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;
      }
      
      .rum-alert-button-primary:hover {
        background: #2563eb;
      }
    `;
      
      const styleSheet = document.createElement('style');
      styleSheet.textContent = styles;
      document.head.appendChild(styleSheet);
    }

    /**
     * Show error alert to user
     */
    showErrorAlert(errorData) {
      if (!this.config.showUserAlerts || !this.alertContainer) return;
      
      const alertType = this.getAlertTypeFromSeverity(errorData.severity);
      const alertId = ++this.alertCounter;
      
      const alert = this.createAlert({
        id: alertId,
        type: alertType,
        title: this.getErrorTitle(errorData),
        message: this.getErrorMessage(errorData),
        actions: this.getErrorActions(errorData)
      });
      
      this.alertContainer.appendChild(alert);
      this.activeAlerts.set(alertId, alert);
      
      // Show alert with animation
      setTimeout(() => {
        alert.classList.add('rum-alert-visible');
      }, 10);
      
      // Auto-hide after duration (unless it's critical)
      if (errorData.severity !== ERROR_SEVERITY.CRITICAL) {
        setTimeout(() => {
          this.hideAlert(alertId);
        }, this.config.alertDuration);
      }
      
      return alertId;
    }

    /**
     * Create alert element
     */
    createAlert({ id, type, title, message, actions }) {
      const alert = document.createElement('div');
      alert.className = `rum-alert rum-alert-${type}`;
      alert.dataset.alertId = id;
      
      const header = document.createElement('div');
      header.className = 'rum-alert-header';
      
      const titleElement = document.createElement('h4');
      titleElement.className = 'rum-alert-title';
      titleElement.textContent = title;
      
      const closeButton = document.createElement('button');
      closeButton.className = 'rum-alert-close';
      closeButton.innerHTML = '×';
      closeButton.onclick = () => this.hideAlert(id);
      
      header.appendChild(titleElement);
      header.appendChild(closeButton);
      
      const messageElement = document.createElement('div');
      messageElement.className = 'rum-alert-message';
      messageElement.textContent = message;
      
      alert.appendChild(header);
      alert.appendChild(messageElement);
      
      if (actions && actions.length > 0) {
        const actionsContainer = document.createElement('div');
        actionsContainer.className = 'rum-alert-actions';
        
        actions.forEach(action => {
          const button = document.createElement('button');
          button.className = `rum-alert-button ${action.primary ? 'rum-alert-button-primary' : ''}`;
          button.textContent = action.text;
          button.onclick = () => {
            action.handler();
            if (action.closeOnClick !== false) {
              this.hideAlert(id);
            }
          };
          actionsContainer.appendChild(button);
        });
        
        alert.appendChild(actionsContainer);
      }
      
      return alert;
    }

    /**
     * Hide alert
     */
    hideAlert(alertId) {
      const alert = this.activeAlerts.get(alertId);
      if (!alert) return;
      
      alert.classList.remove('rum-alert-visible');
      
      setTimeout(() => {
        if (alert.parentNode) {
          alert.parentNode.removeChild(alert);
        }
        this.activeAlerts.delete(alertId);
      }, 300);
    }

    /**
     * Get alert type from error severity
     */
    getAlertTypeFromSeverity(severity) {
      switch (severity) {
        case ERROR_SEVERITY.CRITICAL:
          return ALERT_TYPES.ERROR;
        case ERROR_SEVERITY.HIGH:
          return ALERT_TYPES.ERROR;
        case ERROR_SEVERITY.MEDIUM:
          return ALERT_TYPES.WARNING;
        case ERROR_SEVERITY.LOW:
          return ALERT_TYPES.INFO;
        default:
          return ALERT_TYPES.INFO;
      }
    }

    /**
     * Get error title for display
     */
    getErrorTitle(errorData) {
      switch (errorData.severity) {
        case ERROR_SEVERITY.CRITICAL:
          return 'Critical Error Detected';
        case ERROR_SEVERITY.HIGH:
          return 'Error Occurred';
        case ERROR_SEVERITY.MEDIUM:
          return 'Warning';
        case ERROR_SEVERITY.LOW:
          return 'Notice';
        default:
          return 'Error';
      }
    }

    /**
     * Get user-friendly error message
     */
    getErrorMessage(errorData) {
      switch (errorData.type) {
        case 'javascript':
          return 'A JavaScript error occurred that may affect functionality.';
        case 'network':
          return 'A network request failed. Please check your connection.';
        case 'resource':
          return 'A resource failed to load properly.';
        default:
          return 'An unexpected error occurred.';
      }
    }

    /**
     * Get error recovery actions
     */
    getErrorActions(errorData) {
      const actions = [];
      
      // Always provide reload option for critical errors
      if (errorData.severity === ERROR_SEVERITY.CRITICAL) {
        actions.push({
          text: 'Reload Page',
          primary: true,
          handler: () => window.location.reload()
        });
      }
      
      // Retry action for network errors
      if (errorData.type === 'network') {
        actions.push({
          text: 'Retry',
          primary: true,
          handler: () => {
            // Trigger a custom retry event
            window.dispatchEvent(new CustomEvent('rum:retry', {
              detail: { errorData }
            }));
          }
        });
      }
      
      // Contact support action
      actions.push({
        text: 'Contact Support',
        handler: () => {
          // Trigger a custom support event
          window.dispatchEvent(new CustomEvent('rum:contact-support', {
            detail: { errorData }
          }));
        }
      });
      
      return actions;
    }

    /**
     * Show success message
     */
    showSuccess(message) {
      if (!this.config.showUserAlerts || !this.alertContainer) return;
      
      const alertId = ++this.alertCounter;
      const alert = this.createAlert({
        id: alertId,
        type: ALERT_TYPES.SUCCESS,
        title: 'Success',
        message,
        actions: []
      });
      
      this.alertContainer.appendChild(alert);
      this.activeAlerts.set(alertId, alert);
      
      setTimeout(() => {
        alert.classList.add('rum-alert-visible');
      }, 10);
      
      setTimeout(() => {
        this.hideAlert(alertId);
      }, 3000);
      
      return alertId;
    }

    /**
     * Clear all alerts
     */
    clearAllAlerts() {
      this.activeAlerts.forEach((alert, id) => {
        this.hideAlert(id);
      });
    }

    /**
     * Destroy UI and clean up
     */
    destroy() {
      this.clearAllAlerts();
      
      if (this.alertContainer && this.alertContainer.parentNode) {
        this.alertContainer.parentNode.removeChild(this.alertContainer);
      }
    }
  }

  /**
   * RUM Script - Real User Monitoring with Error Logging and Alerting
   */


  class RUMScript {
    constructor(userConfig = {}) {
      // Merge user config with defaults
      this.config = deepMerge(DEFAULT_CONFIG, userConfig);
      
      // Generate session ID
      this.sessionId = generateSessionId();
      
      // Initialize components
      this.logger = null;
      this.errorCapture = null;
      this.ui = null;
      
      // Error tracking
      this.errorCounts = new Map();
      this.lastAlertTime = 0;
      
      // Throttled alert function
      this.throttledAlert = throttle((errorData) => {
        this.handleCriticalError(errorData);
      }, 5000); // Max one alert per 5 seconds
      
      // Initialize if DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.initialize());
      } else {
        this.initialize();
      }
    }

    /**
     * Initialize RUM script
     */
    initialize() {
      try {
        if (this.config.debug) {
          console.log('RUM: Initializing with config', this.config);
        }
        
        // Initialize logger
        this.logger = new ErrorLogger(this.config, this.sessionId);
        
        // Initialize UI
        this.ui = new UserInterface(this.config);
        
        // Initialize error capture with custom error handler
        this.errorCapture = new ErrorCapture(this.config, {
          logError: (errorData) => this.handleError(errorData)
        });
        
        // Set up page visibility change handler
        this.setupPageVisibilityHandler();
        
        // Set up beforeunload handler
        this.setupBeforeUnloadHandler();
        
        // Set up custom event listeners
        this.setupCustomEventListeners();
        
        if (this.config.debug) {
          console.log('RUM: Initialized successfully');
        }
        
        // Expose global methods
        this.exposeGlobalMethods();
        
      } catch (error) {
        console.error('RUM: Failed to initialize', error);
      }
    }

    /**
     * Handle captured errors
     */
    handleError(errorData) {
      // Log error
      this.logger.logError(errorData);
      
      // Track error frequency
      this.trackErrorFrequency(errorData);
      
      // Show user alert for critical/high severity errors
      if (errorData.severity === ERROR_SEVERITY.CRITICAL || 
          errorData.severity === ERROR_SEVERITY.HIGH) {
        this.ui.showErrorAlert(errorData);
      }
      
      // Check for alert threshold
      if (this.shouldTriggerAlert()) {
        this.throttledAlert(errorData);
      }
    }

    /**
     * Track error frequency for alerting
     */
    trackErrorFrequency(errorData) {
      const now = Date.now();
      const timeWindow = this.config.timeWindow;
      
      // Clean old entries
      for (const [timestamp, count] of this.errorCounts.entries()) {
        if (now - timestamp > timeWindow) {
          this.errorCounts.delete(timestamp);
        }
      }
      
      // Add current error
      const windowStart = Math.floor(now / timeWindow) * timeWindow;
      const currentCount = this.errorCounts.get(windowStart) || 0;
      this.errorCounts.set(windowStart, currentCount + 1);
    }

    /**
     * Check if alert threshold is exceeded
     */
    shouldTriggerAlert() {
      const totalErrors = Array.from(this.errorCounts.values())
        .reduce((sum, count) => sum + count, 0);
      
      return totalErrors >= this.config.errorThreshold;
    }

    /**
     * Handle critical errors that require immediate attention
     */
    handleCriticalError(errorData) {
      const now = Date.now();
      
      // Prevent spam alerts
      if (now - this.lastAlertTime < 60000) { // 1 minute cooldown
        return;
      }
      
      this.lastAlertTime = now;
      
      // Trigger custom event for external handling
      window.dispatchEvent(new CustomEvent('rum:critical-error', {
        detail: {
          errorData,
          sessionId: this.sessionId,
          errorCount: Array.from(this.errorCounts.values())
            .reduce((sum, count) => sum + count, 0)
        }
      }));
      
      if (this.config.debug) {
        console.warn('RUM: Critical error threshold exceeded', errorData);
      }
    }

    /**
     * Set up page visibility change handler
     */
    setupPageVisibilityHandler() {
      document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
          // Page is hidden, flush any pending errors
          this.logger.sendBatch();
        }
      });
    }

    /**
     * Set up beforeunload handler
     */
    setupBeforeUnloadHandler() {
      window.addEventListener('beforeunload', () => {
        // Send any remaining errors before page unload
        this.logger.sendBatch();
      });
    }

    /**
     * Set up custom event listeners
     */
    setupCustomEventListeners() {
      // Retry event handler
      window.addEventListener('rum:retry', (event) => {
        const { errorData } = event.detail;
        
        if (this.config.debug) {
          console.log('RUM: Retry requested for error', errorData);
        }
        
        // Show success message after retry
        setTimeout(() => {
          this.ui.showSuccess('Retry completed successfully');
        }, 1000);
      });
      
      // Contact support event handler
      window.addEventListener('rum:contact-support', (event) => {
        const { errorData } = event.detail;
        
        if (this.config.debug) {
          console.log('RUM: Support contact requested for error', errorData);
        }
        
        // You can customize this to open a support form, email, etc.
        const supportUrl = `mailto:<EMAIL>?subject=Error Report&body=Error ID: ${errorData.errorId}%0ASession ID: ${this.sessionId}%0AError: ${encodeURIComponent(errorData.message)}`;
        window.open(supportUrl);
      });
    }

    /**
     * Expose global methods for external use
     */
    exposeGlobalMethods() {
      // Make RUM methods available globally
      window.RUM = {
        // Log custom error
        logError: (message, details = {}) => {
          this.errorCapture.logCustomError(message, details);
        },
        
        // Get error statistics
        getStats: () => {
          return {
            ...this.logger.getStats(),
            config: this.config,
            errorFrequency: Object.fromEntries(this.errorCounts)
          };
        },
        
        // Update configuration
        updateConfig: (newConfig) => {
          this.config = deepMerge(this.config, newConfig);
          if (this.config.debug) {
            console.log('RUM: Configuration updated', this.config);
          }
        },
        
        // Clear error queue
        clearErrors: () => {
          this.logger.clearQueue();
          this.errorCounts.clear();
          this.ui.clearAllAlerts();
        },
        
        // Destroy RUM instance
        destroy: () => {
          this.destroy();
        }
      };
    }

    /**
     * Get current statistics
     */
    getStats() {
      return {
        sessionId: this.sessionId,
        config: this.config,
        logger: this.logger ? this.logger.getStats() : null,
        errorFrequency: Object.fromEntries(this.errorCounts),
        lastAlertTime: this.lastAlertTime
      };
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig) {
      this.config = deepMerge(this.config, newConfig);
      
      if (this.config.debug) {
        console.log('RUM: Configuration updated', this.config);
      }
    }

    /**
     * Destroy RUM instance and clean up
     */
    destroy() {
      if (this.config.debug) {
        console.log('RUM: Destroying instance');
      }
      
      // Clean up components
      if (this.errorCapture) {
        this.errorCapture.destroy();
      }
      
      if (this.logger) {
        this.logger.destroy();
      }
      
      if (this.ui) {
        this.ui.destroy();
      }
      
      // Clear data
      this.errorCounts.clear();
      
      // Remove global methods
      if (window.RUM) {
        delete window.RUM;
      }
    }
  }

  // Auto-initialize if config is provided via data attributes
  document.addEventListener('DOMContentLoaded', () => {
    const scriptTag = document.querySelector('script[data-rum-config]');
    if (scriptTag) {
      try {
        const config = JSON.parse(scriptTag.dataset.rumConfig);
        new RUMScript(config);
      } catch (error) {
        console.error('RUM: Failed to parse config from script tag', error);
      }
    }
  });

  exports.RUMScript = RUMScript;
  exports.default = RUMScript;

  Object.defineProperty(exports, '__esModule', { value: true });

}));
//# sourceMappingURL=rum-script.js.map
