{"version": 3, "file": "rum-script.esm.js", "sources": ["../src/config.js", "../src/utils.js", "../src/errorCapture.js", "../src/errorLogger.js", "../src/userInterface.js", "../src/index.js"], "sourcesContent": ["/**\n * Default configuration for RUM Script\n */\nexport const DEFAULT_CONFIG = {\n  // Server endpoints\n  logEndpoint: '/api/errors',\n  baseUrl: 'http://localhost:3001',\n\n  // Project settings\n  projectId: 'demo_project_001',\n\n  // Error thresholds\n  errorThreshold: 5,\n  timeWindow: 5 * 60 * 1000, // 5 minutes in milliseconds\n\n  // Environment settings\n  environment: 'production',\n  enableLogging: true,\n  enableConsoleCapture: true,\n  enableNetworkCapture: true,\n  enableResourceCapture: true,\n\n  // Performance settings\n  maxErrorsPerSession: 100,\n  batchSize: 10,\n  batchTimeout: 5000, // 5 seconds\n\n  // User interface settings\n  showUserAlerts: true,\n  alertPosition: 'top-right',\n  alertDuration: 5000,\n\n  // Security settings\n  apiKey: 'demo_api_key_12345',\n  sanitizeData: true,\n  excludeUrls: [],\n  excludeMessages: [],\n\n  // Retry settings\n  maxRetries: 3,\n  retryDelay: 1000,\n\n  // Debug settings\n  debug: false,\n  verbose: false\n};\n\n/**\n * Error severity levels\n */\nexport const ERROR_SEVERITY = {\n  LOW: 'low',\n  MEDIUM: 'medium',\n  HIGH: 'high',\n  CRITICAL: 'critical'\n};\n\n/**\n * Error types\n */\nexport const ERROR_TYPES = {\n  JAVASCRIPT: 'javascript',\n  NETWORK: 'network',\n  RESOURCE: 'resource',\n  CONSOLE: 'console',\n  UNHANDLED_REJECTION: 'unhandled_rejection',\n  CUSTOM: 'custom'\n};\n\n/**\n * Alert types\n */\nexport const ALERT_TYPES = {\n  ERROR: 'error',\n  WARNING: 'warning',\n  INFO: 'info',\n  SUCCESS: 'success'\n};\n", "/**\n * Utility functions for RUM Script\n */\n\n/**\n * Generate a unique session ID\n */\nexport function generateSessionId() {\n  return 'rum_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n}\n\n/**\n * Get current timestamp in ISO format\n */\nexport function getCurrentTimestamp() {\n  return new Date().toISOString();\n}\n\n/**\n * Sanitize error data to remove sensitive information\n */\nexport function sanitizeErrorData(data, config) {\n  if (!config.sanitizeData) return data;\n\n  const sanitized = { ...data };\n\n  // Remove potential sensitive data from stack traces\n  if (sanitized.stack) {\n    sanitized.stack = sanitized.stack.replace(/\\/Users\\/<USER>\\/]+/g, '/Users/<USER>');\n    sanitized.stack = sanitized.stack.replace(/\\/home\\/<USER>\\/]+/g, '/home/<USER>');\n  }\n\n  // Remove sensitive URL parameters\n  if (sanitized.url) {\n    try {\n      const url = new URL(sanitized.url);\n      const sensitiveParams = ['token', 'key', 'password', 'secret', 'auth'];\n      sensitiveParams.forEach(param => {\n        if (url.searchParams.has(param)) {\n          url.searchParams.set(param, '***');\n        }\n      });\n      sanitized.url = url.toString();\n    } catch (e) {\n      // Invalid URL, keep as is\n    }\n  }\n\n  return sanitized;\n}\n\n/**\n * Check if error should be ignored based on configuration\n */\nexport function shouldIgnoreError(error, config) {\n  // Check excluded URLs\n  if (config.excludeUrls.length > 0) {\n    const currentUrl = window.location.href;\n    if (config.excludeUrls.some(pattern => {\n      if (typeof pattern === 'string') {\n        return currentUrl.includes(pattern);\n      }\n      if (pattern instanceof RegExp) {\n        return pattern.test(currentUrl);\n      }\n      return false;\n    })) {\n      return true;\n    }\n  }\n\n  // Check excluded messages\n  if (config.excludeMessages.length > 0 && error.message) {\n    if (config.excludeMessages.some(pattern => {\n      if (typeof pattern === 'string') {\n        return error.message.includes(pattern);\n      }\n      if (pattern instanceof RegExp) {\n        return pattern.test(error.message);\n      }\n      return false;\n    })) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Determine error severity based on error details\n */\nexport function determineErrorSeverity(error) {\n  // Import here to avoid circular dependency\n  const ERROR_SEVERITY = {\n    LOW: 'low',\n    MEDIUM: 'medium',\n    HIGH: 'high',\n    CRITICAL: 'critical'\n  };\n\n  // Critical errors that break functionality\n  if (error.message && (\n    error.message.includes('ChunkLoadError') ||\n    error.message.includes('Loading chunk') ||\n    error.message.includes('Cannot read property') ||\n    error.message.includes('is not a function')\n  )) {\n    return ERROR_SEVERITY.CRITICAL;\n  }\n\n  // High severity for network errors\n  if (error.type === 'network' && error.status >= 500) {\n    return ERROR_SEVERITY.HIGH;\n  }\n\n  // Medium severity for client errors\n  if (error.type === 'network' && error.status >= 400) {\n    return ERROR_SEVERITY.MEDIUM;\n  }\n\n  // Default to low severity\n  return ERROR_SEVERITY.LOW;\n}\n\n/**\n * Debounce function to limit function calls\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Throttle function to limit function calls\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function () {\n    const args = arguments;\n    const context = this;\n    if (!inThrottle) {\n      func.apply(context, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Deep merge objects\n */\nexport function deepMerge(target, source) {\n  const output = Object.assign({}, target);\n  if (isObject(target) && isObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isObject(source[key])) {\n        if (!(key in target))\n          Object.assign(output, { [key]: source[key] });\n        else\n          output[key] = deepMerge(target[key], source[key]);\n      } else {\n        Object.assign(output, { [key]: source[key] });\n      }\n    });\n  }\n  return output;\n}\n\nfunction isObject(item) {\n  return item && typeof item === 'object' && !Array.isArray(item);\n}\n", "/**\n * Error capture functionality for RUM Script\n */\n\nimport { ERROR_TYPES, ERROR_SEVERITY } from './config.js';\nimport { getCurrentTimestamp, determineErrorSeverity } from './utils.js';\n\nexport class ErrorCapture {\n  constructor(config, logger) {\n    this.config = config;\n    this.logger = logger;\n    this.originalConsoleError = console.error;\n    this.originalConsoleWarn = console.warn;\n    this.originalFetch = window.fetch;\n    this.originalXHROpen = XMLHttpRequest.prototype.open;\n    this.originalXHRSend = XMLHttpRequest.prototype.send;\n    \n    this.setupErrorHandlers();\n  }\n\n  /**\n   * Set up all error handlers\n   */\n  setupErrorHandlers() {\n    this.setupJavaScriptErrorHandler();\n    this.setupUnhandledRejectionHandler();\n    this.setupResourceErrorHandler();\n    \n    if (this.config.enableConsoleCapture) {\n      this.setupConsoleErrorHandler();\n    }\n    \n    if (this.config.enableNetworkCapture) {\n      this.setupNetworkErrorHandler();\n    }\n  }\n\n  /**\n   * Handle JavaScript errors\n   */\n  setupJavaScriptErrorHandler() {\n    window.addEventListener('error', (event) => {\n      const errorData = {\n        type: ERROR_TYPES.JAVASCRIPT,\n        message: event.message,\n        filename: event.filename,\n        lineno: event.lineno,\n        colno: event.colno,\n        stack: event.error ? event.error.stack : null,\n        timestamp: getCurrentTimestamp(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        severity: determineErrorSeverity({\n          message: event.message,\n          type: ERROR_TYPES.JAVASCRIPT\n        })\n      };\n      \n      this.logger.logError(errorData);\n    });\n  }\n\n  /**\n   * Handle unhandled promise rejections\n   */\n  setupUnhandledRejectionHandler() {\n    window.addEventListener('unhandledrejection', (event) => {\n      const errorData = {\n        type: ERROR_TYPES.UNHANDLED_REJECTION,\n        message: event.reason ? event.reason.toString() : 'Unhandled Promise Rejection',\n        stack: event.reason && event.reason.stack ? event.reason.stack : null,\n        timestamp: getCurrentTimestamp(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        severity: ERROR_SEVERITY.HIGH\n      };\n      \n      this.logger.logError(errorData);\n    });\n  }\n\n  /**\n   * Handle resource loading errors\n   */\n  setupResourceErrorHandler() {\n    if (!this.config.enableResourceCapture) return;\n    \n    window.addEventListener('error', (event) => {\n      // Only handle resource errors (not JavaScript errors)\n      if (event.target !== window && event.target.tagName) {\n        const errorData = {\n          type: ERROR_TYPES.RESOURCE,\n          message: `Failed to load ${event.target.tagName.toLowerCase()}: ${event.target.src || event.target.href}`,\n          resourceType: event.target.tagName.toLowerCase(),\n          resourceUrl: event.target.src || event.target.href,\n          timestamp: getCurrentTimestamp(),\n          url: window.location.href,\n          userAgent: navigator.userAgent,\n          severity: ERROR_SEVERITY.MEDIUM\n        };\n        \n        this.logger.logError(errorData);\n      }\n    }, true); // Use capture phase\n  }\n\n  /**\n   * Handle console errors\n   */\n  setupConsoleErrorHandler() {\n    const self = this;\n    \n    console.error = function(...args) {\n      const errorData = {\n        type: ERROR_TYPES.CONSOLE,\n        message: args.map(arg => \n          typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n        ).join(' '),\n        timestamp: getCurrentTimestamp(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        severity: ERROR_SEVERITY.MEDIUM\n      };\n      \n      self.logger.logError(errorData);\n      \n      // Call original console.error\n      self.originalConsoleError.apply(console, args);\n    };\n    \n    console.warn = function(...args) {\n      if (self.config.verbose) {\n        const errorData = {\n          type: ERROR_TYPES.CONSOLE,\n          message: '[WARN] ' + args.map(arg => \n            typeof arg === 'object' ? JSON.stringify(arg) : String(arg)\n          ).join(' '),\n          timestamp: getCurrentTimestamp(),\n          url: window.location.href,\n          userAgent: navigator.userAgent,\n          severity: ERROR_SEVERITY.LOW\n        };\n        \n        self.logger.logError(errorData);\n      }\n      \n      // Call original console.warn\n      self.originalConsoleWarn.apply(console, args);\n    };\n  }\n\n  /**\n   * Handle network errors\n   */\n  setupNetworkErrorHandler() {\n    this.setupFetchErrorHandler();\n    this.setupXHRErrorHandler();\n  }\n\n  /**\n   * Handle fetch API errors\n   */\n  setupFetchErrorHandler() {\n    const self = this;\n    \n    window.fetch = function(...args) {\n      return self.originalFetch.apply(this, args)\n        .then(response => {\n          if (!response.ok) {\n            const errorData = {\n              type: ERROR_TYPES.NETWORK,\n              message: `Fetch error: ${response.status} ${response.statusText}`,\n              url: args[0],\n              status: response.status,\n              statusText: response.statusText,\n              timestamp: getCurrentTimestamp(),\n              pageUrl: window.location.href,\n              userAgent: navigator.userAgent,\n              severity: response.status >= 500 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM\n            };\n            \n            self.logger.logError(errorData);\n          }\n          return response;\n        })\n        .catch(error => {\n          const errorData = {\n            type: ERROR_TYPES.NETWORK,\n            message: `Fetch error: ${error.message}`,\n            url: args[0],\n            error: error.toString(),\n            timestamp: getCurrentTimestamp(),\n            pageUrl: window.location.href,\n            userAgent: navigator.userAgent,\n            severity: ERROR_SEVERITY.HIGH\n          };\n          \n          self.logger.logError(errorData);\n          throw error;\n        });\n    };\n  }\n\n  /**\n   * Handle XMLHttpRequest errors\n   */\n  setupXHRErrorHandler() {\n    const self = this;\n    \n    XMLHttpRequest.prototype.open = function(method, url, ...args) {\n      this._rumMethod = method;\n      this._rumUrl = url;\n      return self.originalXHROpen.apply(this, [method, url, ...args]);\n    };\n    \n    XMLHttpRequest.prototype.send = function(...args) {\n      const xhr = this;\n      \n      xhr.addEventListener('error', function() {\n        const errorData = {\n          type: ERROR_TYPES.NETWORK,\n          message: `XHR error: ${xhr._rumMethod} ${xhr._rumUrl}`,\n          method: xhr._rumMethod,\n          url: xhr._rumUrl,\n          timestamp: getCurrentTimestamp(),\n          pageUrl: window.location.href,\n          userAgent: navigator.userAgent,\n          severity: ERROR_SEVERITY.HIGH\n        };\n        \n        self.logger.logError(errorData);\n      });\n      \n      xhr.addEventListener('load', function() {\n        if (xhr.status >= 400) {\n          const errorData = {\n            type: ERROR_TYPES.NETWORK,\n            message: `XHR error: ${xhr.status} ${xhr.statusText}`,\n            method: xhr._rumMethod,\n            url: xhr._rumUrl,\n            status: xhr.status,\n            statusText: xhr.statusText,\n            timestamp: getCurrentTimestamp(),\n            pageUrl: window.location.href,\n            userAgent: navigator.userAgent,\n            severity: xhr.status >= 500 ? ERROR_SEVERITY.HIGH : ERROR_SEVERITY.MEDIUM\n          };\n          \n          self.logger.logError(errorData);\n        }\n      });\n      \n      return self.originalXHRSend.apply(this, args);\n    };\n  }\n\n  /**\n   * Manually log a custom error\n   */\n  logCustomError(message, details = {}) {\n    const errorData = {\n      type: ERROR_TYPES.CUSTOM,\n      message,\n      ...details,\n      timestamp: getCurrentTimestamp(),\n      url: window.location.href,\n      userAgent: navigator.userAgent,\n      severity: details.severity || ERROR_SEVERITY.MEDIUM\n    };\n    \n    this.logger.logError(errorData);\n  }\n\n  /**\n   * Clean up error handlers\n   */\n  destroy() {\n    // Restore original functions\n    console.error = this.originalConsoleError;\n    console.warn = this.originalConsoleWarn;\n    window.fetch = this.originalFetch;\n    XMLHttpRequest.prototype.open = this.originalXHROpen;\n    XMLHttpRequest.prototype.send = this.originalXHRSend;\n  }\n}\n", "/**\n * Error logging functionality for RUM Script\n */\n\nimport { sanitizeErrorData, shouldIgnoreError, debounce } from './utils.js';\n\nexport class ErrorLogger {\n  constructor(config, sessionId) {\n    this.config = config;\n    this.sessionId = sessionId;\n    this.errorQueue = [];\n    this.errorCount = 0;\n    this.lastErrorTime = null;\n    this.retryQueue = [];\n\n    // Debounced batch send function\n    this.debouncedSend = debounce(() => this.sendBatch(), this.config.batchTimeout);\n\n    // Set up periodic batch sending\n    this.batchInterval = setInterval(() => {\n      if (this.errorQueue.length > 0) {\n        this.sendBatch();\n      }\n    }, this.config.batchTimeout);\n  }\n\n  /**\n   * Log an error\n   */\n  logError(errorData) {\n    // Check if logging is enabled\n    if (!this.config.enableLogging) return;\n\n    // Check if error should be ignored\n    if (shouldIgnoreError(errorData, this.config)) return;\n\n    // Check if we've exceeded max errors per session\n    if (this.errorCount >= this.config.maxErrorsPerSession) {\n      if (this.config.debug) {\n        console.warn('RUM: Max errors per session exceeded');\n      }\n      return;\n    }\n\n    // Sanitize error data\n    const sanitizedError = sanitizeErrorData(errorData, this.config);\n\n    // Add session information\n    const enrichedError = {\n      ...sanitizedError,\n      sessionId: this.sessionId,\n      errorId: this.generateErrorId(),\n      environment: this.config.environment,\n      viewport: {\n        width: window.innerWidth,\n        height: window.innerHeight\n      },\n      screen: {\n        width: window.screen.width,\n        height: window.screen.height\n      }\n    };\n\n    // Add to queue\n    this.errorQueue.push(enrichedError);\n    this.errorCount++;\n    this.lastErrorTime = Date.now();\n\n    if (this.config.debug) {\n      console.log('RUM: Error logged', enrichedError);\n    }\n\n    // Send immediately if batch size reached or critical error\n    if (this.errorQueue.length >= this.config.batchSize ||\n      errorData.severity === 'critical') {\n      this.sendBatch();\n    } else {\n      // Otherwise, debounce the send\n      this.debouncedSend();\n    }\n  }\n\n  /**\n   * Send batch of errors to server\n   */\n  async sendBatch() {\n    if (this.errorQueue.length === 0) return;\n\n    const batch = [...this.errorQueue];\n    this.errorQueue = [];\n\n    const payload = {\n      errors: batch,\n      sessionId: this.sessionId,\n      projectId: this.config.projectId,\n      timestamp: new Date().toISOString(),\n      batchId: this.generateBatchId()\n    };\n\n    try {\n      await this.sendToServer(payload);\n\n      if (this.config.debug) {\n        console.log('RUM: Batch sent successfully', payload);\n      }\n    } catch (error) {\n      if (this.config.debug) {\n        console.error('RUM: Failed to send batch', error);\n      }\n\n      // Add to retry queue\n      this.retryQueue.push({\n        payload,\n        attempts: 0,\n        timestamp: Date.now()\n      });\n\n      this.processRetryQueue();\n    }\n  }\n\n  /**\n   * Send payload to server\n   */\n  async sendToServer(payload) {\n    const url = `${this.config.baseUrl}${this.config.logEndpoint}`;\n\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n\n    if (this.config.apiKey) {\n      headers['Authorization'] = `Bearer ${this.config.apiKey}`;\n    }\n\n    const response = await fetch(url, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(payload),\n      mode: 'cors'\n    });\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n    }\n\n    return response.json();\n  }\n\n  /**\n   * Process retry queue\n   */\n  async processRetryQueue() {\n    const now = Date.now();\n    const itemsToRetry = [];\n\n    this.retryQueue = this.retryQueue.filter(item => {\n      if (item.attempts >= this.config.maxRetries) {\n        if (this.config.debug) {\n          console.warn('RUM: Max retries exceeded for batch', item.payload.batchId);\n        }\n        return false; // Remove from queue\n      }\n\n      const timeSinceLastAttempt = now - item.timestamp;\n      const retryDelay = this.config.retryDelay * Math.pow(2, item.attempts); // Exponential backoff\n\n      if (timeSinceLastAttempt >= retryDelay) {\n        itemsToRetry.push(item);\n        return false; // Remove from queue (will be re-added if retry fails)\n      }\n\n      return true; // Keep in queue\n    });\n\n    // Process retry items\n    for (const item of itemsToRetry) {\n      try {\n        await this.sendToServer(item.payload);\n\n        if (this.config.debug) {\n          console.log('RUM: Retry successful for batch', item.payload.batchId);\n        }\n      } catch (error) {\n        if (this.config.debug) {\n          console.error('RUM: Retry failed for batch', item.payload.batchId, error);\n        }\n\n        // Add back to retry queue with incremented attempts\n        this.retryQueue.push({\n          ...item,\n          attempts: item.attempts + 1,\n          timestamp: now\n        });\n      }\n    }\n\n    // Schedule next retry processing if there are items in queue\n    if (this.retryQueue.length > 0) {\n      setTimeout(() => this.processRetryQueue(), this.config.retryDelay);\n    }\n  }\n\n  /**\n   * Generate unique error ID\n   */\n  generateErrorId() {\n    return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n  }\n\n  /**\n   * Generate unique batch ID\n   */\n  generateBatchId() {\n    return 'batch_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n  }\n\n  /**\n   * Get error statistics\n   */\n  getStats() {\n    return {\n      totalErrors: this.errorCount,\n      queuedErrors: this.errorQueue.length,\n      retryQueueSize: this.retryQueue.length,\n      lastErrorTime: this.lastErrorTime,\n      sessionId: this.sessionId\n    };\n  }\n\n  /**\n   * Clear error queue\n   */\n  clearQueue() {\n    this.errorQueue = [];\n    this.retryQueue = [];\n  }\n\n  /**\n   * Destroy logger and clean up\n   */\n  destroy() {\n    // Send any remaining errors\n    if (this.errorQueue.length > 0) {\n      this.sendBatch();\n    }\n\n    // Clear intervals\n    if (this.batchInterval) {\n      clearInterval(this.batchInterval);\n    }\n\n    // Clear queues\n    this.clearQueue();\n  }\n}\n", "/**\n * User interface for error alerts and recovery options\n */\n\nimport { ALERT_TYPES, ERROR_SEVERITY } from './config.js';\n\nexport class UserInterface {\n  constructor(config) {\n    this.config = config;\n    this.alertContainer = null;\n    this.activeAlerts = new Map();\n    this.alertCounter = 0;\n    \n    this.createAlertContainer();\n    this.injectStyles();\n  }\n\n  /**\n   * Create alert container\n   */\n  createAlertContainer() {\n    if (!this.config.showUserAlerts) return;\n    \n    this.alertContainer = document.createElement('div');\n    this.alertContainer.id = 'rum-alert-container';\n    this.alertContainer.className = `rum-alerts rum-alerts-${this.config.alertPosition}`;\n    \n    document.body.appendChild(this.alertContainer);\n  }\n\n  /**\n   * Inject CSS styles\n   */\n  injectStyles() {\n    if (!this.config.showUserAlerts) return;\n    \n    const styles = `\n      .rum-alerts {\n        position: fixed;\n        z-index: 10000;\n        pointer-events: none;\n        max-width: 400px;\n      }\n      \n      .rum-alerts-top-right {\n        top: 20px;\n        right: 20px;\n      }\n      \n      .rum-alerts-top-left {\n        top: 20px;\n        left: 20px;\n      }\n      \n      .rum-alerts-bottom-right {\n        bottom: 20px;\n        right: 20px;\n      }\n      \n      .rum-alerts-bottom-left {\n        bottom: 20px;\n        left: 20px;\n      }\n      \n      .rum-alert {\n        background: white;\n        border-radius: 8px;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        margin-bottom: 12px;\n        padding: 16px;\n        pointer-events: auto;\n        transform: translateX(100%);\n        transition: all 0.3s ease;\n        border-left: 4px solid #ccc;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        line-height: 1.4;\n      }\n      \n      .rum-alert.rum-alert-visible {\n        transform: translateX(0);\n      }\n      \n      .rum-alert-error {\n        border-left-color: #ef4444;\n      }\n      \n      .rum-alert-warning {\n        border-left-color: #f59e0b;\n      }\n      \n      .rum-alert-info {\n        border-left-color: #3b82f6;\n      }\n      \n      .rum-alert-success {\n        border-left-color: #10b981;\n      }\n      \n      .rum-alert-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-bottom: 8px;\n      }\n      \n      .rum-alert-title {\n        font-weight: 600;\n        color: #1f2937;\n        margin: 0;\n      }\n      \n      .rum-alert-close {\n        background: none;\n        border: none;\n        font-size: 18px;\n        cursor: pointer;\n        color: #6b7280;\n        padding: 0;\n        width: 20px;\n        height: 20px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      \n      .rum-alert-close:hover {\n        color: #374151;\n      }\n      \n      .rum-alert-message {\n        color: #4b5563;\n        margin-bottom: 12px;\n      }\n      \n      .rum-alert-actions {\n        display: flex;\n        gap: 8px;\n        flex-wrap: wrap;\n      }\n      \n      .rum-alert-button {\n        background: #f3f4f6;\n        border: 1px solid #d1d5db;\n        border-radius: 4px;\n        padding: 6px 12px;\n        font-size: 12px;\n        cursor: pointer;\n        transition: background-color 0.2s;\n      }\n      \n      .rum-alert-button:hover {\n        background: #e5e7eb;\n      }\n      \n      .rum-alert-button-primary {\n        background: #3b82f6;\n        color: white;\n        border-color: #3b82f6;\n      }\n      \n      .rum-alert-button-primary:hover {\n        background: #2563eb;\n      }\n    `;\n    \n    const styleSheet = document.createElement('style');\n    styleSheet.textContent = styles;\n    document.head.appendChild(styleSheet);\n  }\n\n  /**\n   * Show error alert to user\n   */\n  showErrorAlert(errorData) {\n    if (!this.config.showUserAlerts || !this.alertContainer) return;\n    \n    const alertType = this.getAlertTypeFromSeverity(errorData.severity);\n    const alertId = ++this.alertCounter;\n    \n    const alert = this.createAlert({\n      id: alertId,\n      type: alertType,\n      title: this.getErrorTitle(errorData),\n      message: this.getErrorMessage(errorData),\n      actions: this.getErrorActions(errorData)\n    });\n    \n    this.alertContainer.appendChild(alert);\n    this.activeAlerts.set(alertId, alert);\n    \n    // Show alert with animation\n    setTimeout(() => {\n      alert.classList.add('rum-alert-visible');\n    }, 10);\n    \n    // Auto-hide after duration (unless it's critical)\n    if (errorData.severity !== ERROR_SEVERITY.CRITICAL) {\n      setTimeout(() => {\n        this.hideAlert(alertId);\n      }, this.config.alertDuration);\n    }\n    \n    return alertId;\n  }\n\n  /**\n   * Create alert element\n   */\n  createAlert({ id, type, title, message, actions }) {\n    const alert = document.createElement('div');\n    alert.className = `rum-alert rum-alert-${type}`;\n    alert.dataset.alertId = id;\n    \n    const header = document.createElement('div');\n    header.className = 'rum-alert-header';\n    \n    const titleElement = document.createElement('h4');\n    titleElement.className = 'rum-alert-title';\n    titleElement.textContent = title;\n    \n    const closeButton = document.createElement('button');\n    closeButton.className = 'rum-alert-close';\n    closeButton.innerHTML = '×';\n    closeButton.onclick = () => this.hideAlert(id);\n    \n    header.appendChild(titleElement);\n    header.appendChild(closeButton);\n    \n    const messageElement = document.createElement('div');\n    messageElement.className = 'rum-alert-message';\n    messageElement.textContent = message;\n    \n    alert.appendChild(header);\n    alert.appendChild(messageElement);\n    \n    if (actions && actions.length > 0) {\n      const actionsContainer = document.createElement('div');\n      actionsContainer.className = 'rum-alert-actions';\n      \n      actions.forEach(action => {\n        const button = document.createElement('button');\n        button.className = `rum-alert-button ${action.primary ? 'rum-alert-button-primary' : ''}`;\n        button.textContent = action.text;\n        button.onclick = () => {\n          action.handler();\n          if (action.closeOnClick !== false) {\n            this.hideAlert(id);\n          }\n        };\n        actionsContainer.appendChild(button);\n      });\n      \n      alert.appendChild(actionsContainer);\n    }\n    \n    return alert;\n  }\n\n  /**\n   * Hide alert\n   */\n  hideAlert(alertId) {\n    const alert = this.activeAlerts.get(alertId);\n    if (!alert) return;\n    \n    alert.classList.remove('rum-alert-visible');\n    \n    setTimeout(() => {\n      if (alert.parentNode) {\n        alert.parentNode.removeChild(alert);\n      }\n      this.activeAlerts.delete(alertId);\n    }, 300);\n  }\n\n  /**\n   * Get alert type from error severity\n   */\n  getAlertTypeFromSeverity(severity) {\n    switch (severity) {\n      case ERROR_SEVERITY.CRITICAL:\n        return ALERT_TYPES.ERROR;\n      case ERROR_SEVERITY.HIGH:\n        return ALERT_TYPES.ERROR;\n      case ERROR_SEVERITY.MEDIUM:\n        return ALERT_TYPES.WARNING;\n      case ERROR_SEVERITY.LOW:\n        return ALERT_TYPES.INFO;\n      default:\n        return ALERT_TYPES.INFO;\n    }\n  }\n\n  /**\n   * Get error title for display\n   */\n  getErrorTitle(errorData) {\n    switch (errorData.severity) {\n      case ERROR_SEVERITY.CRITICAL:\n        return 'Critical Error Detected';\n      case ERROR_SEVERITY.HIGH:\n        return 'Error Occurred';\n      case ERROR_SEVERITY.MEDIUM:\n        return 'Warning';\n      case ERROR_SEVERITY.LOW:\n        return 'Notice';\n      default:\n        return 'Error';\n    }\n  }\n\n  /**\n   * Get user-friendly error message\n   */\n  getErrorMessage(errorData) {\n    switch (errorData.type) {\n      case 'javascript':\n        return 'A JavaScript error occurred that may affect functionality.';\n      case 'network':\n        return 'A network request failed. Please check your connection.';\n      case 'resource':\n        return 'A resource failed to load properly.';\n      default:\n        return 'An unexpected error occurred.';\n    }\n  }\n\n  /**\n   * Get error recovery actions\n   */\n  getErrorActions(errorData) {\n    const actions = [];\n    \n    // Always provide reload option for critical errors\n    if (errorData.severity === ERROR_SEVERITY.CRITICAL) {\n      actions.push({\n        text: 'Reload Page',\n        primary: true,\n        handler: () => window.location.reload()\n      });\n    }\n    \n    // Retry action for network errors\n    if (errorData.type === 'network') {\n      actions.push({\n        text: 'Retry',\n        primary: true,\n        handler: () => {\n          // Trigger a custom retry event\n          window.dispatchEvent(new CustomEvent('rum:retry', {\n            detail: { errorData }\n          }));\n        }\n      });\n    }\n    \n    // Contact support action\n    actions.push({\n      text: 'Contact Support',\n      handler: () => {\n        // Trigger a custom support event\n        window.dispatchEvent(new CustomEvent('rum:contact-support', {\n          detail: { errorData }\n        }));\n      }\n    });\n    \n    return actions;\n  }\n\n  /**\n   * Show success message\n   */\n  showSuccess(message) {\n    if (!this.config.showUserAlerts || !this.alertContainer) return;\n    \n    const alertId = ++this.alertCounter;\n    const alert = this.createAlert({\n      id: alertId,\n      type: ALERT_TYPES.SUCCESS,\n      title: 'Success',\n      message,\n      actions: []\n    });\n    \n    this.alertContainer.appendChild(alert);\n    this.activeAlerts.set(alertId, alert);\n    \n    setTimeout(() => {\n      alert.classList.add('rum-alert-visible');\n    }, 10);\n    \n    setTimeout(() => {\n      this.hideAlert(alertId);\n    }, 3000);\n    \n    return alertId;\n  }\n\n  /**\n   * Clear all alerts\n   */\n  clearAllAlerts() {\n    this.activeAlerts.forEach((alert, id) => {\n      this.hideAlert(id);\n    });\n  }\n\n  /**\n   * Destroy UI and clean up\n   */\n  destroy() {\n    this.clearAllAlerts();\n    \n    if (this.alertContainer && this.alertContainer.parentNode) {\n      this.alertContainer.parentNode.removeChild(this.alertContainer);\n    }\n  }\n}\n", "/**\n * RUM Script - Real User Monitoring with Error Logging and Alerting\n */\n\nimport { DEFAULT_CONFIG, ERROR_SEVERITY } from './config.js';\nimport { generateSessionId, deepMerge, throttle } from './utils.js';\nimport { ErrorCapture } from './errorCapture.js';\nimport { ErrorLogger } from './errorLogger.js';\nimport { UserInterface } from './userInterface.js';\n\nexport class RUMScript {\n  constructor(userConfig = {}) {\n    // Merge user config with defaults\n    this.config = deepMerge(DEFAULT_CONFIG, userConfig);\n    \n    // Generate session ID\n    this.sessionId = generateSessionId();\n    \n    // Initialize components\n    this.logger = null;\n    this.errorCapture = null;\n    this.ui = null;\n    \n    // Error tracking\n    this.errorCounts = new Map();\n    this.lastAlertTime = 0;\n    \n    // Throttled alert function\n    this.throttledAlert = throttle((errorData) => {\n      this.handleCriticalError(errorData);\n    }, 5000); // Max one alert per 5 seconds\n    \n    // Initialize if DOM is ready\n    if (document.readyState === 'loading') {\n      document.addEventListener('DOMContentLoaded', () => this.initialize());\n    } else {\n      this.initialize();\n    }\n  }\n\n  /**\n   * Initialize RUM script\n   */\n  initialize() {\n    try {\n      if (this.config.debug) {\n        console.log('RUM: Initializing with config', this.config);\n      }\n      \n      // Initialize logger\n      this.logger = new ErrorLogger(this.config, this.sessionId);\n      \n      // Initialize UI\n      this.ui = new UserInterface(this.config);\n      \n      // Initialize error capture with custom error handler\n      this.errorCapture = new ErrorCapture(this.config, {\n        logError: (errorData) => this.handleError(errorData)\n      });\n      \n      // Set up page visibility change handler\n      this.setupPageVisibilityHandler();\n      \n      // Set up beforeunload handler\n      this.setupBeforeUnloadHandler();\n      \n      // Set up custom event listeners\n      this.setupCustomEventListeners();\n      \n      if (this.config.debug) {\n        console.log('RUM: Initialized successfully');\n      }\n      \n      // Expose global methods\n      this.exposeGlobalMethods();\n      \n    } catch (error) {\n      console.error('RUM: Failed to initialize', error);\n    }\n  }\n\n  /**\n   * Handle captured errors\n   */\n  handleError(errorData) {\n    // Log error\n    this.logger.logError(errorData);\n    \n    // Track error frequency\n    this.trackErrorFrequency(errorData);\n    \n    // Show user alert for critical/high severity errors\n    if (errorData.severity === ERROR_SEVERITY.CRITICAL || \n        errorData.severity === ERROR_SEVERITY.HIGH) {\n      this.ui.showErrorAlert(errorData);\n    }\n    \n    // Check for alert threshold\n    if (this.shouldTriggerAlert()) {\n      this.throttledAlert(errorData);\n    }\n  }\n\n  /**\n   * Track error frequency for alerting\n   */\n  trackErrorFrequency(errorData) {\n    const now = Date.now();\n    const timeWindow = this.config.timeWindow;\n    \n    // Clean old entries\n    for (const [timestamp, count] of this.errorCounts.entries()) {\n      if (now - timestamp > timeWindow) {\n        this.errorCounts.delete(timestamp);\n      }\n    }\n    \n    // Add current error\n    const windowStart = Math.floor(now / timeWindow) * timeWindow;\n    const currentCount = this.errorCounts.get(windowStart) || 0;\n    this.errorCounts.set(windowStart, currentCount + 1);\n  }\n\n  /**\n   * Check if alert threshold is exceeded\n   */\n  shouldTriggerAlert() {\n    const totalErrors = Array.from(this.errorCounts.values())\n      .reduce((sum, count) => sum + count, 0);\n    \n    return totalErrors >= this.config.errorThreshold;\n  }\n\n  /**\n   * Handle critical errors that require immediate attention\n   */\n  handleCriticalError(errorData) {\n    const now = Date.now();\n    \n    // Prevent spam alerts\n    if (now - this.lastAlertTime < 60000) { // 1 minute cooldown\n      return;\n    }\n    \n    this.lastAlertTime = now;\n    \n    // Trigger custom event for external handling\n    window.dispatchEvent(new CustomEvent('rum:critical-error', {\n      detail: {\n        errorData,\n        sessionId: this.sessionId,\n        errorCount: Array.from(this.errorCounts.values())\n          .reduce((sum, count) => sum + count, 0)\n      }\n    }));\n    \n    if (this.config.debug) {\n      console.warn('RUM: Critical error threshold exceeded', errorData);\n    }\n  }\n\n  /**\n   * Set up page visibility change handler\n   */\n  setupPageVisibilityHandler() {\n    document.addEventListener('visibilitychange', () => {\n      if (document.hidden) {\n        // Page is hidden, flush any pending errors\n        this.logger.sendBatch();\n      }\n    });\n  }\n\n  /**\n   * Set up beforeunload handler\n   */\n  setupBeforeUnloadHandler() {\n    window.addEventListener('beforeunload', () => {\n      // Send any remaining errors before page unload\n      this.logger.sendBatch();\n    });\n  }\n\n  /**\n   * Set up custom event listeners\n   */\n  setupCustomEventListeners() {\n    // Retry event handler\n    window.addEventListener('rum:retry', (event) => {\n      const { errorData } = event.detail;\n      \n      if (this.config.debug) {\n        console.log('RUM: Retry requested for error', errorData);\n      }\n      \n      // Show success message after retry\n      setTimeout(() => {\n        this.ui.showSuccess('Retry completed successfully');\n      }, 1000);\n    });\n    \n    // Contact support event handler\n    window.addEventListener('rum:contact-support', (event) => {\n      const { errorData } = event.detail;\n      \n      if (this.config.debug) {\n        console.log('RUM: Support contact requested for error', errorData);\n      }\n      \n      // You can customize this to open a support form, email, etc.\n      const supportUrl = `mailto:<EMAIL>?subject=Error Report&body=Error ID: ${errorData.errorId}%0ASession ID: ${this.sessionId}%0AError: ${encodeURIComponent(errorData.message)}`;\n      window.open(supportUrl);\n    });\n  }\n\n  /**\n   * Expose global methods for external use\n   */\n  exposeGlobalMethods() {\n    // Make RUM methods available globally\n    window.RUM = {\n      // Log custom error\n      logError: (message, details = {}) => {\n        this.errorCapture.logCustomError(message, details);\n      },\n      \n      // Get error statistics\n      getStats: () => {\n        return {\n          ...this.logger.getStats(),\n          config: this.config,\n          errorFrequency: Object.fromEntries(this.errorCounts)\n        };\n      },\n      \n      // Update configuration\n      updateConfig: (newConfig) => {\n        this.config = deepMerge(this.config, newConfig);\n        if (this.config.debug) {\n          console.log('RUM: Configuration updated', this.config);\n        }\n      },\n      \n      // Clear error queue\n      clearErrors: () => {\n        this.logger.clearQueue();\n        this.errorCounts.clear();\n        this.ui.clearAllAlerts();\n      },\n      \n      // Destroy RUM instance\n      destroy: () => {\n        this.destroy();\n      }\n    };\n  }\n\n  /**\n   * Get current statistics\n   */\n  getStats() {\n    return {\n      sessionId: this.sessionId,\n      config: this.config,\n      logger: this.logger ? this.logger.getStats() : null,\n      errorFrequency: Object.fromEntries(this.errorCounts),\n      lastAlertTime: this.lastAlertTime\n    };\n  }\n\n  /**\n   * Update configuration\n   */\n  updateConfig(newConfig) {\n    this.config = deepMerge(this.config, newConfig);\n    \n    if (this.config.debug) {\n      console.log('RUM: Configuration updated', this.config);\n    }\n  }\n\n  /**\n   * Destroy RUM instance and clean up\n   */\n  destroy() {\n    if (this.config.debug) {\n      console.log('RUM: Destroying instance');\n    }\n    \n    // Clean up components\n    if (this.errorCapture) {\n      this.errorCapture.destroy();\n    }\n    \n    if (this.logger) {\n      this.logger.destroy();\n    }\n    \n    if (this.ui) {\n      this.ui.destroy();\n    }\n    \n    // Clear data\n    this.errorCounts.clear();\n    \n    // Remove global methods\n    if (window.RUM) {\n      delete window.RUM;\n    }\n  }\n}\n\n// Auto-initialize if config is provided via data attributes\ndocument.addEventListener('DOMContentLoaded', () => {\n  const scriptTag = document.querySelector('script[data-rum-config]');\n  if (scriptTag) {\n    try {\n      const config = JSON.parse(scriptTag.dataset.rumConfig);\n      new RUMScript(config);\n    } catch (error) {\n      console.error('RUM: Failed to parse config from script tag', error);\n    }\n  }\n});\n\n// Export for module usage\nexport default RUMScript;\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACO,MAAM,cAAc,GAAG;AAC9B;AACA,EAAE,WAAW,EAAE,aAAa;AAC5B,EAAE,OAAO,EAAE,uBAAuB;;AAElC;AACA,EAAE,SAAS,EAAE,kBAAkB;;AAE/B;AACA,EAAE,cAAc,EAAE,CAAC;AACnB,EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;;AAE3B;AACA,EAAE,WAAW,EAAE,YAAY;AAC3B,EAAE,aAAa,EAAE,IAAI;AACrB,EAAE,oBAAoB,EAAE,IAAI;AAC5B,EAAE,oBAAoB,EAAE,IAAI;AAC5B,EAAE,qBAAqB,EAAE,IAAI;;AAE7B;AACA,EAAE,mBAAmB,EAAE,GAAG;AAC1B,EAAE,SAAS,EAAE,EAAE;AACf,EAAE,YAAY,EAAE,IAAI;;AAEpB;AACA,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,aAAa,EAAE,WAAW;AAC5B,EAAE,aAAa,EAAE,IAAI;;AAErB;AACA,EAAE,MAAM,EAAE,oBAAoB;AAC9B,EAAE,YAAY,EAAE,IAAI;AACpB,EAAE,WAAW,EAAE,EAAE;AACjB,EAAE,eAAe,EAAE,EAAE;;AAErB;AACA,EAAE,UAAU,EAAE,CAAC;AACf,EAAE,UAAU,EAAE,IAAI;;AAElB;AACA,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACO,MAAM,cAAc,GAAG;AAC9B,EAAE,GAAG,EAAE,KAAK;AACZ,EAAE,MAAM,EAAE,QAAQ;AAClB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACO,MAAM,WAAW,GAAG;AAC3B,EAAE,UAAU,EAAE,YAAY;AAC1B,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,mBAAmB,EAAE,qBAAqB;AAC5C,EAAE,MAAM,EAAE;AACV,CAAC;;AAED;AACA;AACA;AACO,MAAM,WAAW,GAAG;AAC3B,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,OAAO,EAAE;AACX,CAAC;;AC7ED;AACA;AACA;;AAEA;AACA;AACA;AACO,SAAS,iBAAiB,GAAG;AACpC,EAAE,OAAO,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5E;;AAEA;AACA;AACA;AACO,SAAS,mBAAmB,GAAG;AACtC,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACjC;;AAEA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE;AAChD,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,IAAI;;AAEvC,EAAE,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE;;AAE/B;AACA,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE;AACvB,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,YAAY,CAAC;AAC/E,IAAI,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,EAAE,WAAW,CAAC;AAC7E,EAAE;;AAEF;AACA,EAAE,IAAI,SAAS,CAAC,GAAG,EAAE;AACrB,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC;AACxC,MAAM,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC5E,MAAM,eAAe,CAAC,OAAO,CAAC,KAAK,IAAI;AACvC,QAAQ,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACzC,UAAU,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;AAC5C,QAAQ;AACR,MAAM,CAAC,CAAC;AACR,MAAM,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE;AACpC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE;AAChB;AACA,IAAI;AACJ,EAAE;;AAEF,EAAE,OAAO,SAAS;AAClB;;AAEA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE;AACjD;AACA,EAAE,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI;AAC3C,IAAI,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,IAAI;AAC3C,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC3C,MAAM;AACN,MAAM,IAAI,OAAO,YAAY,MAAM,EAAE;AACrC,QAAQ,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;AACvC,MAAM;AACN,MAAM,OAAO,KAAK;AAClB,IAAI,CAAC,CAAC,EAAE;AACR,MAAM,OAAO,IAAI;AACjB,IAAI;AACJ,EAAE;;AAEF;AACA,EAAE,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE;AAC1D,IAAI,IAAI,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,IAAI;AAC/C,MAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACvC,QAAQ,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC9C,MAAM;AACN,MAAM,IAAI,OAAO,YAAY,MAAM,EAAE;AACrC,QAAQ,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC1C,MAAM;AACN,MAAM,OAAO,KAAK;AAClB,IAAI,CAAC,CAAC,EAAE;AACR,MAAM,OAAO,IAAI;AACjB,IAAI;AACJ,EAAE;;AAEF,EAAE,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,KAAK,EAAE;AAC9C;AACA,EAAE,MAAM,cAAc,GAAG;AACzB,IAAI,GAAG,EAAE,KAAK;AACd,IAEI,QAAQ,EAAE;AACd,GAAG;;AAEH;AACA,EAAE,IAAI,KAAK,CAAC,OAAO;AACnB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC;AAClD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB;AAC9C,GAAG,EAAE;AACL,IAAI,OAAO,cAAc,CAAC,QAAQ;AAClC,EAAE;;AAYF;AACA,EAAE,OAAO,cAAc,CAAC,GAAG;AAC3B;;AAEA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE;AACrC,EAAE,IAAI,OAAO;AACb,EAAE,OAAO,SAAS,gBAAgB,CAAC,GAAG,IAAI,EAAE;AAC5C,IAAI,MAAM,KAAK,GAAG,MAAM;AACxB,MAAM,YAAY,CAAC,OAAO,CAAC;AAC3B,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,IAAI,CAAC;AACL,IAAI,YAAY,CAAC,OAAO,CAAC;AACzB,IAAI,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;AACrC,EAAE,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;AACtC,EAAE,IAAI,UAAU;AAChB,EAAE,OAAO,YAAY;AACrB,IAAI,MAAM,IAAI,GAAG,SAAS;AAC1B,IAAI,MAAM,OAAO,GAAG,IAAI;AACxB,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;AAC/B,MAAM,UAAU,GAAG,IAAI;AACvB,MAAM,UAAU,CAAC,MAAM,UAAU,GAAG,KAAK,EAAE,KAAK,CAAC;AACjD,IAAI;AACJ,EAAE,CAAC;AACH;;AAEA;AACA;AACA;AACO,SAAS,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE;AAC1C,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC;AAC1C,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;AACvC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;AACjC,QAAQ,IAAI,EAAE,GAAG,IAAI,MAAM,CAAC;AAC5B,UAAU,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;AACvD;AACA,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;AAC3D,MAAM,CAAC,MAAM;AACb,QAAQ,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;AACrD,MAAM;AACN,IAAI,CAAC,CAAC;AACN,EAAE;AACF,EAAE,OAAO,MAAM;AACf;;AAEA,SAAS,QAAQ,CAAC,IAAI,EAAE;AACxB,EAAE,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AACjE;;AClLA;AACA;AACA;;;AAKO,MAAM,YAAY,CAAC;AAC1B,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,KAAK;AAC7C,IAAI,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,IAAI;AAC3C,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK;AACrC,IAAI,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI;AACxD,IAAI,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI;AACxD;AACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC7B,EAAE;;AAEF;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACtC,IAAI,IAAI,CAAC,8BAA8B,EAAE;AACzC,IAAI,IAAI,CAAC,yBAAyB,EAAE;AACpC;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;AAC1C,MAAM,IAAI,CAAC,wBAAwB,EAAE;AACrC,IAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;AAC1C,MAAM,IAAI,CAAC,wBAAwB,EAAE;AACrC,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,2BAA2B,GAAG;AAChC,IAAI,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK;AAChD,MAAM,MAAM,SAAS,GAAG;AACxB,QAAQ,IAAI,EAAE,WAAW,CAAC,UAAU;AACpC,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAChC,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK;AAC1B,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;AACrD,QAAQ,SAAS,EAAE,mBAAmB,EAAE;AACxC,QAAQ,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACjC,QAAQ,SAAS,EAAE,SAAS,CAAC,SAAS;AACtC,QAAQ,QAAQ,EAAE,sBAAsB,CAAC;AACzC,UAAU,OAAO,EAAE,KAAK,CAAC,OAEjB,CAAC;AACT,OAAO;AACP;AACA,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACrC,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA,EAAE,8BAA8B,GAAG;AACnC,IAAI,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAC,KAAK,KAAK;AAC7D,MAAM,MAAM,SAAS,GAAG;AACxB,QAAQ,IAAI,EAAE,WAAW,CAAC,mBAAmB;AAC7C,QAAQ,OAAO,EAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,6BAA6B;AACvF,QAAQ,KAAK,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI;AAC7E,QAAQ,SAAS,EAAE,mBAAmB,EAAE;AACxC,QAAQ,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACjC,QAAQ,SAAS,EAAE,SAAS,CAAC,SAAS;AACtC,QAAQ,QAAQ,EAAE,cAAc,CAAC;AACjC,OAAO;AACP;AACA,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACrC,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA,EAAE,yBAAyB,GAAG;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;AAC5C;AACA,IAAI,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK;AAChD;AACA,MAAM,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE;AAC3D,QAAQ,MAAM,SAAS,GAAG;AAC1B,UAAU,IAAI,EAAE,WAAW,CAAC,QAAQ;AACpC,UAAU,OAAO,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnH,UAAU,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE;AAC1D,UAAU,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI;AAC5D,UAAU,SAAS,EAAE,mBAAmB,EAAE;AAC1C,UAAU,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACnC,UAAU,SAAS,EAAE,SAAS,CAAC,SAAS;AACxC,UAAU,QAAQ,EAAE,cAAc,CAAC;AACnC,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACvC,MAAM;AACN,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb,EAAE;;AAEF;AACA;AACA;AACA,EAAE,wBAAwB,GAAG;AAC7B,IAAI,MAAM,IAAI,GAAG,IAAI;AACrB;AACA,IAAI,OAAO,CAAC,KAAK,GAAG,SAAS,GAAG,IAAI,EAAE;AACtC,MAAM,MAAM,SAAS,GAAG;AACxB,QAAQ,IAAI,EAAE,WAAW,CAAC,OAAO;AACjC,QAAQ,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG;AAC7B,UAAU,OAAO,GAAG,KAAK,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG;AACpE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;AACnB,QAAQ,SAAS,EAAE,mBAAmB,EAAE;AACxC,QAAQ,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACjC,QAAQ,SAAS,EAAE,SAAS,CAAC,SAAS;AACtC,QAAQ,QAAQ,EAAE,cAAc,CAAC;AACjC,OAAO;AACP;AACA,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACrC;AACA;AACA,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;AACpD,IAAI,CAAC;AACL;AACA,IAAI,OAAO,CAAC,IAAI,GAAG,SAAS,GAAG,IAAI,EAAE;AACrC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC/B,QAAQ,MAAM,SAAS,GAAG;AAC1B,UAAU,IAAI,EAAE,WAAW,CAAC,OAAO;AACnC,UAAU,OAAO,EAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG;AAC3C,YAAY,OAAO,GAAG,KAAK,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG;AACtE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC;AACrB,UAAU,SAAS,EAAE,mBAAmB,EAAE;AAC1C,UAAU,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACnC,UAAU,SAAS,EAAE,SAAS,CAAC,SAAS;AACxC,UAAU,QAAQ,EAAE,cAAc,CAAC;AACnC,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACvC,MAAM;AACN;AACA;AACA,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;AACnD,IAAI,CAAC;AACL,EAAE;;AAEF;AACA;AACA;AACA,EAAE,wBAAwB,GAAG;AAC7B,IAAI,IAAI,CAAC,sBAAsB,EAAE;AACjC,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC/B,EAAE;;AAEF;AACA;AACA;AACA,EAAE,sBAAsB,GAAG;AAC3B,IAAI,MAAM,IAAI,GAAG,IAAI;AACrB;AACA,IAAI,MAAM,CAAC,KAAK,GAAG,SAAS,GAAG,IAAI,EAAE;AACrC,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI;AAChD,SAAS,IAAI,CAAC,QAAQ,IAAI;AAC1B,UAAU,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AAC5B,YAAY,MAAM,SAAS,GAAG;AAC9B,cAAc,IAAI,EAAE,WAAW,CAAC,OAAO;AACvC,cAAc,OAAO,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC/E,cAAc,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1B,cAAc,MAAM,EAAE,QAAQ,CAAC,MAAM;AACrC,cAAc,UAAU,EAAE,QAAQ,CAAC,UAAU;AAC7C,cAAc,SAAS,EAAE,mBAAmB,EAAE;AAC9C,cAAc,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AAC3C,cAAc,SAAS,EAAE,SAAS,CAAC,SAAS;AAC5C,cAAc,QAAQ,EAAE,QAAQ,CAAC,MAAM,IAAI,GAAG,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;AACtF,aAAa;AACb;AACA,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC3C,UAAU;AACV,UAAU,OAAO,QAAQ;AACzB,QAAQ,CAAC;AACT,SAAS,KAAK,CAAC,KAAK,IAAI;AACxB,UAAU,MAAM,SAAS,GAAG;AAC5B,YAAY,IAAI,EAAE,WAAW,CAAC,OAAO;AACrC,YAAY,OAAO,EAAE,CAAC,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACpD,YAAY,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACxB,YAAY,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACnC,YAAY,SAAS,EAAE,mBAAmB,EAAE;AAC5C,YAAY,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACzC,YAAY,SAAS,EAAE,SAAS,CAAC,SAAS;AAC1C,YAAY,QAAQ,EAAE,cAAc,CAAC;AACrC,WAAW;AACX;AACA,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACzC,UAAU,MAAM,KAAK;AACrB,QAAQ,CAAC,CAAC;AACV,IAAI,CAAC;AACL,EAAE;;AAEF;AACA;AACA;AACA,EAAE,oBAAoB,GAAG;AACzB,IAAI,MAAM,IAAI,GAAG,IAAI;AACrB;AACA,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;AACnE,MAAM,IAAI,CAAC,UAAU,GAAG,MAAM;AAC9B,MAAM,IAAI,CAAC,OAAO,GAAG,GAAG;AACxB,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AACrE,IAAI,CAAC;AACL;AACA,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,GAAG,IAAI,EAAE;AACtD,MAAM,MAAM,GAAG,GAAG,IAAI;AACtB;AACA,MAAM,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW;AAC/C,QAAQ,MAAM,SAAS,GAAG;AAC1B,UAAU,IAAI,EAAE,WAAW,CAAC,OAAO;AACnC,UAAU,OAAO,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAChE,UAAU,MAAM,EAAE,GAAG,CAAC,UAAU;AAChC,UAAU,GAAG,EAAE,GAAG,CAAC,OAAO;AAC1B,UAAU,SAAS,EAAE,mBAAmB,EAAE;AAC1C,UAAU,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACvC,UAAU,SAAS,EAAE,SAAS,CAAC,SAAS;AACxC,UAAU,QAAQ,EAAE,cAAc,CAAC;AACnC,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACvC,MAAM,CAAC,CAAC;AACR;AACA,MAAM,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW;AAC9C,QAAQ,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAAE;AAC/B,UAAU,MAAM,SAAS,GAAG;AAC5B,YAAY,IAAI,EAAE,WAAW,CAAC,OAAO;AACrC,YAAY,OAAO,EAAE,CAAC,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AACjE,YAAY,MAAM,EAAE,GAAG,CAAC,UAAU;AAClC,YAAY,GAAG,EAAE,GAAG,CAAC,OAAO;AAC5B,YAAY,MAAM,EAAE,GAAG,CAAC,MAAM;AAC9B,YAAY,UAAU,EAAE,GAAG,CAAC,UAAU;AACtC,YAAY,SAAS,EAAE,mBAAmB,EAAE;AAC5C,YAAY,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AACzC,YAAY,SAAS,EAAE,SAAS,CAAC,SAAS;AAC1C,YAAY,QAAQ,EAAE,GAAG,CAAC,MAAM,IAAI,GAAG,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC;AAC/E,WAAW;AACX;AACA,UAAU,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACzC,QAAQ;AACR,MAAM,CAAC,CAAC;AACR;AACA,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC;AACnD,IAAI,CAAC;AACL,EAAE;;AAEF;AACA;AACA;AACA,EAAE,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,EAAE;AACxC,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,IAAI,EAAE,WAAW,CAAC,MAAM;AAC9B,MAAM,OAAO;AACb,MAAM,GAAG,OAAO;AAChB,MAAM,SAAS,EAAE,mBAAmB,EAAE;AACtC,MAAM,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;AAC/B,MAAM,SAAS,EAAE,SAAS,CAAC,SAAS;AACpC,MAAM,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,cAAc,CAAC;AACnD,KAAK;AACL;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACnC,EAAE;;AAEF;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ;AACA,IAAI,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB;AAC7C,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB;AAC3C,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa;AACrC,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe;AACxD,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe;AACxD,EAAE;AACF;;AC5RA;AACA;AACA;;;AAIO,MAAM,WAAW,CAAC;AACzB,EAAE,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE;AACjC,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;AACxB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC;AACvB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI;AAC7B,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;;AAExB;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;;AAEnF;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,MAAM;AAC3C,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,MAAM;AACN,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;AAChC,EAAE;;AAEF;AACA;AACA;AACA,EAAE,QAAQ,CAAC,SAAS,EAAE;AACtB;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;;AAEpC;AACA,IAAI,IAAI,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;;AAEnD;AACA,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE;AAC5D,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,CAAC,IAAI,CAAC,sCAAsC,CAAC;AAC5D,MAAM;AACN,MAAM;AACN,IAAI;;AAEJ;AACA,IAAI,MAAM,cAAc,GAAG,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC;;AAEpE;AACA,IAAI,MAAM,aAAa,GAAG;AAC1B,MAAM,GAAG,cAAc;AACvB,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;AACrC,MAAM,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;AAC1C,MAAM,QAAQ,EAAE;AAChB,QAAQ,KAAK,EAAE,MAAM,CAAC,UAAU;AAChC,QAAQ,MAAM,EAAE,MAAM,CAAC;AACvB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,KAAK;AAClC,QAAQ,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC;AAC9B;AACA,KAAK;;AAEL;AACA,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC;AACvC,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE;;AAEnC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,aAAa,CAAC;AACrD,IAAI;;AAEJ;AACA,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;AACvD,MAAM,SAAS,CAAC,QAAQ,KAAK,UAAU,EAAE;AACzC,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,IAAI,CAAC,MAAM;AACX;AACA,MAAM,IAAI,CAAC,aAAa,EAAE;AAC1B,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,MAAM,SAAS,GAAG;AACpB,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;;AAEtC,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AACtC,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;;AAExB,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;AACtC,MAAM,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACzC,MAAM,OAAO,EAAE,IAAI,CAAC,eAAe;AACnC,KAAK;;AAEL,IAAI,IAAI;AACR,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;;AAEtC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,OAAO,CAAC;AAC5D,MAAM;AACN,IAAI,CAAC,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACzD,MAAM;;AAEN;AACA,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AAC3B,QAAQ,OAAO;AACf,QAAQ,QAAQ,EAAE,CAAC;AACnB,QAAQ,SAAS,EAAE,IAAI,CAAC,GAAG;AAC3B,OAAO,CAAC;;AAER,MAAM,IAAI,CAAC,iBAAiB,EAAE;AAC9B,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,MAAM,YAAY,CAAC,OAAO,EAAE;AAC9B,IAAI,MAAM,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;;AAElE,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,cAAc,EAAE;AACtB,KAAK;;AAEL,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC5B,MAAM,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC/D,IAAI;;AAEJ,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE;AACtC,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO;AACb,MAAM,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACnC,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC;;AAEN,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACtB,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;AACxE,IAAI;;AAEJ,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE;AAC1B,EAAE;;AAEF;AACA;AACA;AACA,EAAE,MAAM,iBAAiB,GAAG;AAC5B,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1B,IAAI,MAAM,YAAY,GAAG,EAAE;;AAE3B,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI;AACrD,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AACnD,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC/B,UAAU,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AACnF,QAAQ;AACR,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM;;AAEN,MAAM,MAAM,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAAC,SAAS;AACvD,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;AAE7E,MAAM,IAAI,oBAAoB,IAAI,UAAU,EAAE;AAC9C,QAAQ,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/B,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM;;AAEN,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,CAAC,CAAC;;AAEN;AACA,IAAI,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;AACrC,MAAM,IAAI;AACV,QAAQ,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC;;AAE7C,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC/B,UAAU,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AAC9E,QAAQ;AACR,MAAM,CAAC,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC/B,UAAU,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;AACnF,QAAQ;;AAER;AACA,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AAC7B,UAAU,GAAG,IAAI;AACjB,UAAU,QAAQ,EAAE,IAAI,CAAC,QAAQ,GAAG,CAAC;AACrC,UAAU,SAAS,EAAE;AACrB,SAAS,CAAC;AACV,MAAM;AACN,IAAI;;AAEJ;AACA,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,MAAM,UAAU,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;AACxE,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,OAAO,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC9E,EAAE;;AAEF;AACA;AACA;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,OAAO,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAChF,EAAE;;AAEF;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO;AACX,MAAM,WAAW,EAAE,IAAI,CAAC,UAAU;AAClC,MAAM,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;AAC1C,MAAM,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;AAC5C,MAAM,aAAa,EAAE,IAAI,CAAC,aAAa;AACvC,MAAM,SAAS,EAAE,IAAI,CAAC;AACtB,KAAK;AACL,EAAE;;AAEF;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;AACxB,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE;AACxB,EAAE;;AAEF;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ;AACA,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,IAAI;;AAEJ;AACA,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5B,MAAM,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC;AACvC,IAAI;;AAEJ;AACA,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,EAAE;AACF;;AC/PA;AACA;AACA;;;AAIO,MAAM,aAAa,CAAC;AAC3B,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI;AAC9B,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE;AACjC,IAAI,IAAI,CAAC,YAAY,GAAG,CAAC;AACzB;AACA,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC/B,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,EAAE;;AAEF;AACA;AACA;AACA,EAAE,oBAAoB,GAAG;AACzB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AACrC;AACA,IAAI,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACvD,IAAI,IAAI,CAAC,cAAc,CAAC,EAAE,GAAG,qBAAqB;AAClD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACxF;AACA,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC;AAClD,EAAE;;AAEF;AACA;AACA;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AACrC;AACA,IAAI,MAAM,MAAM,GAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL;AACA,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACtD,IAAI,UAAU,CAAC,WAAW,GAAG,MAAM;AACnC,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;AACzC,EAAE;;AAEF;AACA;AACA;AACA,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,QAAQ,CAAC;AACvE,IAAI,MAAM,OAAO,GAAG,EAAE,IAAI,CAAC,YAAY;AACvC;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;AACnC,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;AAC1C,MAAM,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;AAC9C,MAAM,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,SAAS;AAC7C,KAAK,CAAC;AACN;AACA,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC;AAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AACzC;AACA;AACA,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC;AAC9C,IAAI,CAAC,EAAE,EAAE,CAAC;AACV;AACA;AACA,IAAI,IAAI,SAAS,CAAC,QAAQ,KAAK,cAAc,CAAC,QAAQ,EAAE;AACxD,MAAM,UAAU,CAAC,MAAM;AACvB,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC/B,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;AACnC,IAAI;AACJ;AACA,IAAI,OAAO,OAAO;AAClB,EAAE;;AAEF;AACA;AACA;AACA,EAAE,WAAW,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;AACrD,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/C,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AACnD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,EAAE;AAC9B;AACA,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAChD,IAAI,MAAM,CAAC,SAAS,GAAG,kBAAkB;AACzC;AACA,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC;AACrD,IAAI,YAAY,CAAC,SAAS,GAAG,iBAAiB;AAC9C,IAAI,YAAY,CAAC,WAAW,GAAG,KAAK;AACpC;AACA,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AACxD,IAAI,WAAW,CAAC,SAAS,GAAG,iBAAiB;AAC7C,IAAI,WAAW,CAAC,SAAS,GAAG,GAAG;AAC/B,IAAI,WAAW,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AAClD;AACA,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC;AACpC,IAAI,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC;AACnC;AACA,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AACxD,IAAI,cAAc,CAAC,SAAS,GAAG,mBAAmB;AAClD,IAAI,cAAc,CAAC,WAAW,GAAG,OAAO;AACxC;AACA,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;AAC7B,IAAI,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC;AACrC;AACA,IAAI,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AACvC,MAAM,MAAM,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC;AAC5D,MAAM,gBAAgB,CAAC,SAAS,GAAG,mBAAmB;AACtD;AACA,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,IAAI;AAChC,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC;AACvD,QAAQ,MAAM,CAAC,SAAS,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,OAAO,GAAG,0BAA0B,GAAG,EAAE,CAAC,CAAC;AACjG,QAAQ,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI;AACxC,QAAQ,MAAM,CAAC,OAAO,GAAG,MAAM;AAC/B,UAAU,MAAM,CAAC,OAAO,EAAE;AAC1B,UAAU,IAAI,MAAM,CAAC,YAAY,KAAK,KAAK,EAAE;AAC7C,YAAY,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AAC9B,UAAU;AACV,QAAQ,CAAC;AACT,QAAQ,gBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC;AAC5C,MAAM,CAAC,CAAC;AACR;AACA,MAAM,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC;AACzC,IAAI;AACJ;AACA,IAAI,OAAO,KAAK;AAChB,EAAE;;AAEF;AACA;AACA;AACA,EAAE,SAAS,CAAC,OAAO,EAAE;AACrB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC;AAChD,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB;AACA,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC;AAC/C;AACA,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE;AAC5B,QAAQ,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC;AAC3C,MAAM;AACN,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC;AACvC,IAAI,CAAC,EAAE,GAAG,CAAC;AACX,EAAE;;AAEF;AACA;AACA;AACA,EAAE,wBAAwB,CAAC,QAAQ,EAAE;AACrC,IAAI,QAAQ,QAAQ;AACpB,MAAM,KAAK,cAAc,CAAC,QAAQ;AAClC,QAAQ,OAAO,WAAW,CAAC,KAAK;AAChC,MAAM,KAAK,cAAc,CAAC,IAAI;AAC9B,QAAQ,OAAO,WAAW,CAAC,KAAK;AAChC,MAAM,KAAK,cAAc,CAAC,MAAM;AAChC,QAAQ,OAAO,WAAW,CAAC,OAAO;AAClC,MAAM,KAAK,cAAc,CAAC,GAAG;AAC7B,QAAQ,OAAO,WAAW,CAAC,IAAI;AAC/B,MAAM;AACN,QAAQ,OAAO,WAAW,CAAC,IAAI;AAC/B;AACA,EAAE;;AAEF;AACA;AACA;AACA,EAAE,aAAa,CAAC,SAAS,EAAE;AAC3B,IAAI,QAAQ,SAAS,CAAC,QAAQ;AAC9B,MAAM,KAAK,cAAc,CAAC,QAAQ;AAClC,QAAQ,OAAO,yBAAyB;AACxC,MAAM,KAAK,cAAc,CAAC,IAAI;AAC9B,QAAQ,OAAO,gBAAgB;AAC/B,MAAM,KAAK,cAAc,CAAC,MAAM;AAChC,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,cAAc,CAAC,GAAG;AAC7B,QAAQ,OAAO,QAAQ;AACvB,MAAM;AACN,QAAQ,OAAO,OAAO;AACtB;AACA,EAAE;;AAEF;AACA;AACA;AACA,EAAE,eAAe,CAAC,SAAS,EAAE;AAC7B,IAAI,QAAQ,SAAS,CAAC,IAAI;AAC1B,MAAM,KAAK,YAAY;AACvB,QAAQ,OAAO,4DAA4D;AAC3E,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,yDAAyD;AACxE,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,qCAAqC;AACpD,MAAM;AACN,QAAQ,OAAO,+BAA+B;AAC9C;AACA,EAAE;;AAEF;AACA;AACA;AACA,EAAE,eAAe,CAAC,SAAS,EAAE;AAC7B,IAAI,MAAM,OAAO,GAAG,EAAE;AACtB;AACA;AACA,IAAI,IAAI,SAAS,CAAC,QAAQ,KAAK,cAAc,CAAC,QAAQ,EAAE;AACxD,MAAM,OAAO,CAAC,IAAI,CAAC;AACnB,QAAQ,IAAI,EAAE,aAAa;AAC3B,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM;AAC7C,OAAO,CAAC;AACR,IAAI;AACJ;AACA;AACA,IAAI,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,EAAE;AACtC,MAAM,OAAO,CAAC,IAAI,CAAC;AACnB,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,OAAO,EAAE,MAAM;AACvB;AACA,UAAU,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,WAAW,EAAE;AAC5D,YAAY,MAAM,EAAE,EAAE,SAAS;AAC/B,WAAW,CAAC,CAAC;AACb,QAAQ;AACR,OAAO,CAAC;AACR,IAAI;AACJ;AACA;AACA,IAAI,OAAO,CAAC,IAAI,CAAC;AACjB,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,OAAO,EAAE,MAAM;AACrB;AACA,QAAQ,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,qBAAqB,EAAE;AACpE,UAAU,MAAM,EAAE,EAAE,SAAS;AAC7B,SAAS,CAAC,CAAC;AACX,MAAM;AACN,KAAK,CAAC;AACN;AACA,IAAI,OAAO,OAAO;AAClB,EAAE;;AAEF;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAC7D;AACA,IAAI,MAAM,OAAO,GAAG,EAAE,IAAI,CAAC,YAAY;AACvC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC;AACnC,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,IAAI,EAAE,WAAW,CAAC,OAAO;AAC/B,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO;AACb,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN;AACA,IAAI,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC;AAC1C,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC;AACzC;AACA,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC;AAC9C,IAAI,CAAC,EAAE,EAAE,CAAC;AACV;AACA,IAAI,UAAU,CAAC,MAAM;AACrB,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC7B,IAAI,CAAC,EAAE,IAAI,CAAC;AACZ;AACA,IAAI,OAAO,OAAO;AAClB,EAAE;;AAEF;AACA;AACA;AACA,EAAE,cAAc,GAAG;AACnB,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK;AAC7C,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;AACxB,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,cAAc,EAAE;AACzB;AACA,IAAI,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE;AAC/D,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC;AACrE,IAAI;AACJ,EAAE;AACF;;ACnaA;AACA;AACA;;;AAQO,MAAM,SAAS,CAAC;AACvB,EAAE,WAAW,CAAC,UAAU,GAAG,EAAE,EAAE;AAC/B;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,cAAc,EAAE,UAAU,CAAC;AACvD;AACA;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,iBAAiB,EAAE;AACxC;AACA;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI;AACtB,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI;AAC5B,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI;AAClB;AACA;AACA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE;AAChC,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC;AAC1B;AACA;AACA,IAAI,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,CAAC,SAAS,KAAK;AAClD,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;AACzC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACb;AACA;AACA,IAAI,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;AAC3C,MAAM,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AAC5E,IAAI,CAAC,MAAM;AACX,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI;AACR,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,MAAM,CAAC;AACjE,MAAM;AACN;AACA;AACA,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC;AAChE;AACA;AACA,MAAM,IAAI,CAAC,EAAE,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;AAC9C;AACA;AACA,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE;AACxD,QAAQ,QAAQ,EAAE,CAAC,SAAS,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS;AAC3D,OAAO,CAAC;AACR;AACA;AACA,MAAM,IAAI,CAAC,0BAA0B,EAAE;AACvC;AACA;AACA,MAAM,IAAI,CAAC,wBAAwB,EAAE;AACrC;AACA;AACA,MAAM,IAAI,CAAC,yBAAyB,EAAE;AACtC;AACA,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;AACpD,MAAM;AACN;AACA;AACA,MAAM,IAAI,CAAC,mBAAmB,EAAE;AAChC;AACA,IAAI,CAAC,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACvD,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;AACnC;AACA;AACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC;AACvC;AACA;AACA,IAAI,IAAI,SAAS,CAAC,QAAQ,KAAK,cAAc,CAAC,QAAQ;AACtD,QAAQ,SAAS,CAAC,QAAQ,KAAK,cAAc,CAAC,IAAI,EAAE;AACpD,MAAM,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,SAAS,CAAC;AACvC,IAAI;AACJ;AACA;AACA,IAAI,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;AACnC,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACpC,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,SAAS,EAAE;AACjC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1B,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU;AAC7C;AACA;AACA,IAAI,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,EAAE;AACjE,MAAM,IAAI,GAAG,GAAG,SAAS,GAAG,UAAU,EAAE;AACxC,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;AAC1C,MAAM;AACN,IAAI;AACJ;AACA;AACA,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,UAAU;AACjE,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC;AAC/D,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY,GAAG,CAAC,CAAC;AACvD,EAAE;;AAEF;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AAC5D,OAAO,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC;AAC7C;AACA,IAAI,OAAO,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc;AACpD,EAAE;;AAEF;AACA;AACA;AACA,EAAE,mBAAmB,CAAC,SAAS,EAAE;AACjC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1B;AACA;AACA,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,GAAG,KAAK,EAAE;AAC1C,MAAM;AACN,IAAI;AACJ;AACA,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;AAC5B;AACA;AACA,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,oBAAoB,EAAE;AAC/D,MAAM,MAAM,EAAE;AACd,QAAQ,SAAS;AACjB,QAAQ,SAAS,EAAE,IAAI,CAAC,SAAS;AACjC,QAAQ,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AACxD,WAAW,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK,GAAG,GAAG,KAAK,EAAE,CAAC;AAChD;AACA,KAAK,CAAC,CAAC;AACP;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC3B,MAAM,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,SAAS,CAAC;AACvE,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,0BAA0B,GAAG;AAC/B,IAAI,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;AACxD,MAAM,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC3B;AACA,QAAQ,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAC/B,MAAM;AACN,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA,EAAE,wBAAwB,GAAG;AAC7B,IAAI,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM;AAClD;AACA,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;AAC7B,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA,EAAE,yBAAyB,GAAG;AAC9B;AACA,IAAI,MAAM,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,KAAK,KAAK;AACpD,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC,MAAM;AACxC;AACA,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,SAAS,CAAC;AAChE,MAAM;AACN;AACA;AACA,MAAM,UAAU,CAAC,MAAM;AACvB,QAAQ,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,8BAA8B,CAAC;AAC3D,MAAM,CAAC,EAAE,IAAI,CAAC;AACd,IAAI,CAAC,CAAC;AACN;AACA;AACA,IAAI,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC,KAAK,KAAK;AAC9D,MAAM,MAAM,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC,MAAM;AACxC;AACA,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,SAAS,CAAC;AAC1E,MAAM;AACN;AACA;AACA,MAAM,MAAM,UAAU,GAAG,CAAC,+DAA+D,EAAE,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;AAChM,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7B,IAAI,CAAC,CAAC;AACN,EAAE;;AAEF;AACA;AACA;AACA,EAAE,mBAAmB,GAAG;AACxB;AACA,IAAI,MAAM,CAAC,GAAG,GAAG;AACjB;AACA,MAAM,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK;AAC3C,QAAQ,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC;AAC1D,MAAM,CAAC;AACP;AACA;AACA,MAAM,QAAQ,EAAE,MAAM;AACtB,QAAQ,OAAO;AACf,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACnC,UAAU,MAAM,EAAE,IAAI,CAAC,MAAM;AAC7B,UAAU,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW;AAC7D,SAAS;AACT,MAAM,CAAC;AACP;AACA;AACA,MAAM,YAAY,EAAE,CAAC,SAAS,KAAK;AACnC,QAAQ,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AACvD,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC/B,UAAU,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,MAAM,CAAC;AAChE,QAAQ;AACR,MAAM,CAAC;AACP;AACA;AACA,MAAM,WAAW,EAAE,MAAM;AACzB,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AAChC,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AAChC,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE;AAChC,MAAM,CAAC;AACP;AACA;AACA,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM;AACN,KAAK;AACL,EAAE;;AAEF;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO;AACX,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;AACzB,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI;AACzD,MAAM,cAAc,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC;AAC1D,MAAM,aAAa,EAAE,IAAI,CAAC;AAC1B,KAAK;AACL,EAAE;;AAEF;AACA;AACA;AACA,EAAE,YAAY,CAAC,SAAS,EAAE;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC;AACnD;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,MAAM,CAAC;AAC5D,IAAI;AACJ,EAAE;;AAEF;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC;AAC7C,IAAI;AACJ;AACA;AACA,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AAC3B,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AACjC,IAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AAC3B,IAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;AACjB,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;AACvB,IAAI;AACJ;AACA;AACA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AAC5B;AACA;AACA,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE;AACpB,MAAM,OAAO,MAAM,CAAC,GAAG;AACvB,IAAI;AACJ,EAAE;AACF;;AAEA;AACA,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;AACpD,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,yBAAyB,CAAC;AACrE,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5D,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC;AAC3B,IAAI,CAAC,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC;AACzE,IAAI;AACJ,EAAE;AACF,CAAC,CAAC;;;;"}