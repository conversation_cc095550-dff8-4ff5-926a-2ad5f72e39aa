/**
 * Default configuration for RUM Script
 */
export const DEFAULT_CONFIG = {
  // Server endpoints
  logEndpoint: '/api/errors',
  baseUrl: 'http://localhost:3001',

  // Project settings
  projectId: 'demo_project_001',

  // Error thresholds
  errorThreshold: 5,
  timeWindow: 5 * 60 * 1000, // 5 minutes in milliseconds

  // Environment settings
  environment: 'production',
  enableLogging: true,
  enableConsoleCapture: true,
  enableNetworkCapture: true,
  enableResourceCapture: true,

  // Performance settings
  maxErrorsPerSession: 100,
  batchSize: 10,
  batchTimeout: 5000, // 5 seconds

  // User interface settings
  showUserAlerts: true,
  alertPosition: 'top-right',
  alertDuration: 5000,

  // Security settings
  apiKey: 'demo_api_key_12345',
  sanitizeData: true,
  excludeUrls: [],
  excludeMessages: [],

  // Retry settings
  maxRetries: 3,
  retryDelay: 1000,

  // Debug settings
  debug: false,
  verbose: false
};

/**
 * Error severity levels
 */
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Error types
 */
export const ERROR_TYPES = {
  JAVASCRIPT: 'javascript',
  NETWORK: 'network',
  RESOURCE: 'resource',
  CONSOLE: 'console',
  UNHANDLED_REJECTION: 'unhandled_rejection',
  CUSTOM: 'custom'
};

/**
 * Alert types
 */
export const ALERT_TYPES = {
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  SUCCESS: 'success'
};
