/**
 * Error logging functionality for RUM Script
 */

import { sanitizeErrorData, shouldIgnoreError, debounce } from './utils.js';

export class ErrorLogger {
  constructor(config, sessionId) {
    this.config = config;
    this.sessionId = sessionId;
    this.errorQueue = [];
    this.errorCount = 0;
    this.lastErrorTime = null;
    this.retryQueue = [];

    // Debounced batch send function
    this.debouncedSend = debounce(() => this.sendBatch(), this.config.batchTimeout);

    // Set up periodic batch sending
    this.batchInterval = setInterval(() => {
      if (this.errorQueue.length > 0) {
        this.sendBatch();
      }
    }, this.config.batchTimeout);
  }

  /**
   * Log an error
   */
  logError(errorData) {
    // Check if logging is enabled
    if (!this.config.enableLogging) return;

    // Check if error should be ignored
    if (shouldIgnoreError(errorData, this.config)) return;

    // Check if we've exceeded max errors per session
    if (this.errorCount >= this.config.maxErrorsPerSession) {
      if (this.config.debug) {
        console.warn('RUM: Max errors per session exceeded');
      }
      return;
    }

    // Sanitize error data
    const sanitizedError = sanitizeErrorData(errorData, this.config);

    // Add session information
    const enrichedError = {
      ...sanitizedError,
      sessionId: this.sessionId,
      errorId: this.generateErrorId(),
      environment: this.config.environment,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      screen: {
        width: window.screen.width,
        height: window.screen.height
      }
    };

    // Add to queue
    this.errorQueue.push(enrichedError);
    this.errorCount++;
    this.lastErrorTime = Date.now();

    if (this.config.debug) {
      console.log('RUM: Error logged', enrichedError);
    }

    // Send immediately if batch size reached or critical error
    if (this.errorQueue.length >= this.config.batchSize ||
      errorData.severity === 'critical') {
      this.sendBatch();
    } else {
      // Otherwise, debounce the send
      this.debouncedSend();
    }
  }

  /**
   * Send batch of errors to server
   */
  async sendBatch() {
    if (this.errorQueue.length === 0) return;

    const batch = [...this.errorQueue];
    this.errorQueue = [];

    const payload = {
      errors: batch,
      sessionId: this.sessionId,
      projectId: this.config.projectId,
      timestamp: new Date().toISOString(),
      batchId: this.generateBatchId()
    };

    try {
      await this.sendToServer(payload);

      if (this.config.debug) {
        console.log('RUM: Batch sent successfully', payload);
      }
    } catch (error) {
      if (this.config.debug) {
        console.error('RUM: Failed to send batch', error);
      }

      // Add to retry queue
      this.retryQueue.push({
        payload,
        attempts: 0,
        timestamp: Date.now()
      });

      this.processRetryQueue();
    }
  }

  /**
   * Send payload to server
   */
  async sendToServer(payload) {
    const url = `${this.config.baseUrl}${this.config.logEndpoint}`;

    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`;
    }

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload),
      mode: 'cors'
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Process retry queue
   */
  async processRetryQueue() {
    const now = Date.now();
    const itemsToRetry = [];

    this.retryQueue = this.retryQueue.filter(item => {
      if (item.attempts >= this.config.maxRetries) {
        if (this.config.debug) {
          console.warn('RUM: Max retries exceeded for batch', item.payload.batchId);
        }
        return false; // Remove from queue
      }

      const timeSinceLastAttempt = now - item.timestamp;
      const retryDelay = this.config.retryDelay * Math.pow(2, item.attempts); // Exponential backoff

      if (timeSinceLastAttempt >= retryDelay) {
        itemsToRetry.push(item);
        return false; // Remove from queue (will be re-added if retry fails)
      }

      return true; // Keep in queue
    });

    // Process retry items
    for (const item of itemsToRetry) {
      try {
        await this.sendToServer(item.payload);

        if (this.config.debug) {
          console.log('RUM: Retry successful for batch', item.payload.batchId);
        }
      } catch (error) {
        if (this.config.debug) {
          console.error('RUM: Retry failed for batch', item.payload.batchId, error);
        }

        // Add back to retry queue with incremented attempts
        this.retryQueue.push({
          ...item,
          attempts: item.attempts + 1,
          timestamp: now
        });
      }
    }

    // Schedule next retry processing if there are items in queue
    if (this.retryQueue.length > 0) {
      setTimeout(() => this.processRetryQueue(), this.config.retryDelay);
    }
  }

  /**
   * Generate unique error ID
   */
  generateErrorId() {
    return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Generate unique batch ID
   */
  generateBatchId() {
    return 'batch_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get error statistics
   */
  getStats() {
    return {
      totalErrors: this.errorCount,
      queuedErrors: this.errorQueue.length,
      retryQueueSize: this.retryQueue.length,
      lastErrorTime: this.lastErrorTime,
      sessionId: this.sessionId
    };
  }

  /**
   * Clear error queue
   */
  clearQueue() {
    this.errorQueue = [];
    this.retryQueue = [];
  }

  /**
   * Destroy logger and clean up
   */
  destroy() {
    // Send any remaining errors
    if (this.errorQueue.length > 0) {
      this.sendBatch();
    }

    // Clear intervals
    if (this.batchInterval) {
      clearInterval(this.batchInterval);
    }

    // Clear queues
    this.clearQueue();
  }
}
