# RUM Alerting System - Deployment Guide

## 🚀 Production Deployment

### Prerequisites

- Node.js 18+ 
- MongoDB 4.4+ (optional, system can use in-memory storage)
- Email service (Gmail, SendGrid, etc.) for alerts
- SMS service (Twilio) for critical alerts
- SSL certificate for HTTPS

### Server Deployment

#### 1. Environment Setup

Create a production `.env` file:

```bash
# Server Configuration
PORT=3001
NODE_ENV=production

# Database Configuration
MONGODB_URI=mongodb://your-mongodb-host:27017/rum-alerting
DB_NAME=rum_alerting

# Security
JWT_SECRET=your-super-secure-jwt-secret-key
API_KEY=your-production-api-key
CORS_ORIGIN=https://your-website.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Alert Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+**********
ERROR_THRESHOLD=10
TIME_WINDOW_MINUTES=5

# Logging
LOG_LEVEL=warn
LOG_FILE=logs/app.log
```

#### 2. Build and Deploy

```bash
# Install dependencies
npm install --production

# Start with PM2 (recommended)
npm install -g pm2
pm2 start src/index.js --name rum-server

# Or use Docker
docker build -t rum-server .
docker run -d -p 3001:3001 --env-file .env rum-server
```

#### 3. Database Setup

If using MongoDB:

```bash
# Connect to MongoDB
mongo mongodb://your-host:27017/rum-alerting

# Create indexes for better performance
db.errorlogs.createIndex({ "createdAt": -1 })
db.errorlogs.createIndex({ "sessionId": 1, "createdAt": -1 })
db.errorlogs.createIndex({ "type": 1, "severity": 1, "createdAt": -1 })
db.errorlogs.createIndex({ "environment": 1, "createdAt": -1 })

# Set up TTL for automatic cleanup (optional)
db.errorlogs.createIndex({ "createdAt": 1 }, { expireAfterSeconds: 2592000 }) // 30 days
```

### Client Integration

#### 1. Build RUM Script

```bash
cd rum-script
npm install
npm run build
```

#### 2. Host the Script

Upload `dist/rum-script.min.js` to your CDN or static file server.

#### 3. Integrate into Website

Add to your website's `<head>` section:

```html
<script src="https://your-cdn.com/rum-script.min.js"></script>
<script>
  new RUMScript({
    baseUrl: 'https://your-rum-server.com',
    logEndpoint: '/api/errors',
    environment: 'production',
    enableLogging: true,
    enableConsoleCapture: true,
    enableNetworkCapture: true,
    enableResourceCapture: true,
    showUserAlerts: true,
    alertPosition: 'top-right',
    errorThreshold: 10,
    timeWindow: 5 * 60 * 1000,
    apiKey: 'your-production-api-key',
    debug: false
  });
</script>
```

### Docker Deployment

#### Dockerfile for Server

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install --production

COPY src/ ./src/

EXPOSE 3001

CMD ["node", "src/index.js"]
```

#### Docker Compose

```yaml
version: '3.8'
services:
  rum-server:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/rum-alerting
    depends_on:
      - mongo
    restart: unless-stopped

  mongo:
    image: mongo:4.4
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    restart: unless-stopped

volumes:
  mongo-data:
```

### Kubernetes Deployment

#### ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rum-config
data:
  NODE_ENV: "production"
  PORT: "3001"
  MONGODB_URI: "mongodb://mongo-service:27017/rum-alerting"
```

#### Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rum-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rum-server
  template:
    metadata:
      labels:
        app: rum-server
    spec:
      containers:
      - name: rum-server
        image: your-registry/rum-server:latest
        ports:
        - containerPort: 3001
        envFrom:
        - configMapRef:
            name: rum-config
        - secretRef:
            name: rum-secrets
```

### Monitoring and Logging

#### 1. Application Monitoring

```bash
# PM2 monitoring
pm2 monit

# Log monitoring
tail -f logs/app.log

# Health check endpoint
curl https://your-rum-server.com/health
```

#### 2. Performance Monitoring

Set up monitoring for:
- Response times
- Error rates
- Memory usage
- Database performance
- Alert delivery success

#### 3. Log Aggregation

Configure log forwarding to services like:
- ELK Stack (Elasticsearch, Logstash, Kibana)
- Splunk
- DataDog
- New Relic

### Security Hardening

#### 1. Server Security

```bash
# Use HTTPS only
# Configure firewall rules
# Regular security updates
# API rate limiting
# Input validation
```

#### 2. Database Security

```bash
# Enable authentication
# Use SSL/TLS connections
# Regular backups
# Access control
```

#### 3. API Security

```bash
# Strong API keys
# CORS configuration
# Request validation
# Rate limiting
```

### Scaling Considerations

#### 1. Horizontal Scaling

- Load balancer configuration
- Session affinity (not required)
- Database connection pooling
- Stateless server design

#### 2. Database Scaling

- MongoDB replica sets
- Sharding for large datasets
- Read replicas for analytics
- Automated backups

#### 3. CDN Integration

- Host RUM script on CDN
- Global distribution
- Cache optimization
- Failover mechanisms

### Maintenance

#### 1. Regular Tasks

```bash
# Update dependencies
npm audit fix

# Database maintenance
db.runCommand({compact: "errorlogs"})

# Log rotation
logrotate /etc/logrotate.d/rum-server

# Health checks
curl -f https://your-rum-server.com/health || exit 1
```

#### 2. Backup Strategy

```bash
# Database backups
mongodump --uri="mongodb://your-host:27017/rum-alerting"

# Configuration backups
tar -czf config-backup.tar.gz .env ecosystem.config.js

# Automated backup scripts
0 2 * * * /path/to/backup-script.sh
```

### Troubleshooting

#### Common Issues

1. **High Memory Usage**
   - Check error queue sizes
   - Monitor batch processing
   - Adjust batch settings

2. **Database Connection Issues**
   - Verify connection string
   - Check network connectivity
   - Monitor connection pool

3. **Alert Delivery Failures**
   - Verify SMTP/SMS credentials
   - Check rate limits
   - Monitor alert logs

4. **High Error Rates**
   - Check client-side integration
   - Verify API endpoints
   - Monitor server performance

### Performance Optimization

#### 1. Server Optimization

```javascript
// Optimize batch processing
batchSize: 50,
batchTimeout: 2000,

// Connection pooling
maxPoolSize: 20,
minPoolSize: 5,

// Caching
redis: {
  host: 'redis-server',
  port: 6379
}
```

#### 2. Database Optimization

```javascript
// Proper indexing
db.errorlogs.createIndex({ "createdAt": -1, "severity": 1 })

// Aggregation optimization
db.errorlogs.aggregate([
  { $match: { createdAt: { $gte: startDate } } },
  { $group: { _id: "$type", count: { $sum: 1 } } }
])
```

### Success Metrics

Monitor these KPIs:
- Error capture rate (>99%)
- Alert delivery time (<30 seconds)
- Server response time (<100ms)
- System uptime (>99.9%)
- False positive rate (<1%)

This deployment guide ensures a robust, scalable, and secure RUM alerting system in production environments.
