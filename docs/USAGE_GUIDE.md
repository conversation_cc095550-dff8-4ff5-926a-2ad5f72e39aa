# RUM Alerting System - Usage Guide

## 🎯 Overview

The RUM (Real User Monitoring) Alerting System is a comprehensive solution for capturing, logging, and alerting on client-side errors in web applications. It provides real-time error monitoring with intelligent alerting and user recovery features.

## 🚀 Quick Start

### 1. Start the Server

```bash
cd server
npm install
npm run dev
```

The server will start on `http://localhost:3001` and automatically use in-memory storage if MongoDB is not available.

### 2. Start the Demo Application

```bash
cd demo-nextjs
npm install
npm run dev
```

The demo app will start on `http://localhost:3000` (or next available port).

### 3. Integration in Your Website

Add the RUM script to your website:

```html
<script src="/path/to/rum-script.min.js"></script>
<script>
  new RUMScript({
    baseUrl: 'http://your-server.com',
    logEndpoint: '/api/errors',
    environment: 'production',
    enableLogging: true,
    showUserAlerts: true,
    errorThreshold: 5,
    timeWindow: 5 * 60 * 1000, // 5 minutes
    apiKey: 'your-api-key'
  });
</script>
```

## 📋 Features

### ✅ Error Capture
- **JavaScript Errors**: Automatic capture of unhandled exceptions
- **Network Errors**: Failed API calls and resource loading issues
- **Console Errors**: Critical console messages
- **Resource Failures**: Missing images, CSS, JS files
- **Custom Errors**: Manual error logging with custom details

### ✅ Asynchronous Logging
- Non-blocking error transmission
- Batch processing for efficiency
- Retry mechanism with exponential backoff
- Queue management with size limits

### ✅ Smart Alerting
- Threshold-based alert triggering
- Email and SMS notifications
- Cooldown periods to prevent spam
- Severity-based alert routing

### ✅ User Recovery Interface
- Visual error alerts for users
- Retry mechanisms for failed actions
- Page reload options
- Support contact integration

### ✅ Performance Optimized
- Minimal script size (< 50KB minified)
- Async processing
- Debounced error handling
- Memory-efficient storage

### ✅ Security Features
- API key authentication
- Data sanitization
- Rate limiting
- CORS protection

## 🔧 Configuration Options

### RUM Script Configuration

```javascript
{
  // Server Configuration
  baseUrl: 'http://localhost:3001',
  logEndpoint: '/api/errors',
  apiKey: 'your-api-key',
  
  // Environment Settings
  environment: 'production',
  enableLogging: true,
  enableConsoleCapture: true,
  enableNetworkCapture: true,
  enableResourceCapture: true,
  
  // Error Thresholds
  errorThreshold: 5,
  timeWindow: 5 * 60 * 1000, // 5 minutes
  maxErrorsPerSession: 100,
  
  // Batching Settings
  batchSize: 10,
  batchTimeout: 5000, // 5 seconds
  
  // User Interface
  showUserAlerts: true,
  alertPosition: 'top-right',
  alertDuration: 5000,
  
  // Performance
  maxRetries: 3,
  retryDelay: 1000,
  
  // Debug
  debug: false,
  verbose: false
}
```

### Server Configuration (.env)

```bash
# Server
PORT=3001
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/rum-alerting

# Security
API_KEY=your-api-key
CORS_ORIGIN=http://localhost:3000

# Alerts
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=+**********
ERROR_THRESHOLD=5
TIME_WINDOW_MINUTES=5

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SMS (Optional)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
TWILIO_PHONE_NUMBER=+**********
```

## 📊 API Endpoints

### Error Logging
- `POST /api/errors` - Log error batch
- `GET /api/errors` - Retrieve errors with filtering
- `GET /api/errors/:errorId` - Get specific error
- `GET /api/errors/session/:sessionId` - Get session errors

### Statistics
- `GET /api/stats/overview` - General statistics
- `GET /api/stats/trends` - Error trends over time
- `GET /api/stats/top-errors` - Most frequent errors
- `GET /api/stats/sessions` - Session statistics
- `GET /api/stats/health` - System health metrics

### Health Check
- `GET /health` - Server health status

## 🧪 Testing the Demo

1. **Open the demo app** at `http://localhost:3002`
2. **Use the Error Testing Panel** to trigger different types of errors:
   - JavaScript Error: Calls a non-existent function
   - Network Error: Makes a request to a non-existent endpoint
   - Console Error: Logs an error to the console
   - Custom Error: Manually logs a custom error

3. **Watch the RUM Statistics Panel** to see:
   - Total error count
   - Queued errors
   - Session information
   - Error frequency data

4. **Observe the user alerts** that appear for high-severity errors
5. **Check the browser console** to see RUM script activity
6. **Monitor the server logs** to see error processing

## 🔍 Error Types and Severity

### Error Types
- `javascript` - JavaScript runtime errors
- `network` - Network request failures
- `resource` - Resource loading failures
- `console` - Console error messages
- `unhandled_rejection` - Unhandled promise rejections
- `custom` - Manually logged errors

### Severity Levels
- `low` - Minor issues, informational
- `medium` - Moderate issues, warnings
- `high` - Serious issues, errors
- `critical` - Application-breaking issues

## 📈 Monitoring and Alerts

### Alert Triggers
- Error count exceeds threshold within time window
- Critical errors are detected
- Manual alert triggers

### Alert Types
- **Email**: Detailed error reports with HTML formatting
- **SMS**: Concise critical error notifications
- **User Alerts**: In-browser notifications with recovery options

### Alert Cooldowns
- Email: 30 minutes
- SMS: 60 minutes
- User alerts: 5 seconds (throttled)

## 🛠️ Customization

### Custom Error Logging
```javascript
// Log a custom error
window.RUM.logError('Payment processing failed', {
  severity: 'critical',
  component: 'checkout',
  userId: '12345',
  metadata: {
    amount: 99.99,
    paymentMethod: 'credit-card'
  }
});
```

### Custom Event Handlers
```javascript
// Listen for retry events
window.addEventListener('rum:retry', (event) => {
  const { errorData } = event.detail;
  // Handle retry logic
});

// Listen for support contact events
window.addEventListener('rum:contact-support', (event) => {
  const { errorData } = event.detail;
  // Open support form or chat
});
```

### Configuration Updates
```javascript
// Update configuration at runtime
window.RUM.updateConfig({
  errorThreshold: 10,
  showUserAlerts: false
});
```

## 🔒 Security Considerations

1. **API Key Protection**: Store API keys securely, use environment variables
2. **Data Sanitization**: Sensitive data is automatically scrubbed from error logs
3. **Rate Limiting**: Prevents abuse with configurable limits
4. **CORS**: Properly configured for cross-origin requests
5. **Input Validation**: All API inputs are validated and sanitized

## 🚀 Production Deployment

### Server Deployment
1. Set up MongoDB database
2. Configure environment variables
3. Set up email/SMS services (optional)
4. Deploy to your preferred hosting platform
5. Configure SSL/TLS certificates
6. Set up monitoring and logging

### Client Integration
1. Build and minify the RUM script
2. Host the script on your CDN
3. Configure production settings
4. Test error capture and alerting
5. Monitor performance impact

## 📞 Support

For issues, questions, or contributions:
- Check the server logs for error details
- Review the browser console for client-side issues
- Verify configuration settings
- Test with the demo application first

## 🎉 Success Criteria

✅ All acceptance criteria have been met:
- JavaScript error events are captured reliably
- Errors are logged to server with relevant details
- Visual alerts are displayed for critical errors
- Admin alerts are sent when thresholds are exceeded
- Error logging can be toggled by environment
- Script performs without impacting website performance
