"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Studio
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EngagementContextListInstance = exports.EngagementContextInstance = exports.EngagementContextContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../../base/deserialize");
const serialize = require("../../../../../base/serialize");
const utility_1 = require("../../../../../base/utility");
class EngagementContextContextImpl {
    constructor(_version, flowSid, engagementSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(flowSid)) {
            throw new Error("Parameter 'flowSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(engagementSid)) {
            throw new Error("Parameter 'engagementSid' is not valid.");
        }
        this._solution = { flowSid, engagementSid };
        this._uri = `/Flows/${flowSid}/Engagements/${engagementSid}/Context`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new EngagementContextInstance(operationVersion, payload, instance._solution.flowSid, instance._solution.engagementSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EngagementContextContextImpl = EngagementContextContextImpl;
class EngagementContextInstance {
    constructor(_version, payload, flowSid, engagementSid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.context = payload.context;
        this.engagementSid = payload.engagement_sid;
        this.flowSid = payload.flow_sid;
        this.url = payload.url;
        this._solution = { flowSid, engagementSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new EngagementContextContextImpl(this._version, this._solution.flowSid, this._solution.engagementSid);
        return this._context;
    }
    /**
     * Fetch a EngagementContextInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed EngagementContextInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            context: this.context,
            engagementSid: this.engagementSid,
            flowSid: this.flowSid,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.EngagementContextInstance = EngagementContextInstance;
function EngagementContextListInstance(version, flowSid, engagementSid) {
    if (!(0, utility_1.isValidPathParam)(flowSid)) {
        throw new Error("Parameter 'flowSid' is not valid.");
    }
    if (!(0, utility_1.isValidPathParam)(engagementSid)) {
        throw new Error("Parameter 'engagementSid' is not valid.");
    }
    const instance = (() => instance.get());
    instance.get = function get() {
        return new EngagementContextContextImpl(version, flowSid, engagementSid);
    };
    instance._version = version;
    instance._solution = { flowSid, engagementSid };
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.EngagementContextListInstance = EngagementContextListInstance;
