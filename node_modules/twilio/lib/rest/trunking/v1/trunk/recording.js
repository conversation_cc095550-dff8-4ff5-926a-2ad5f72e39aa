"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trunking
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecordingListInstance = exports.RecordingInstance = exports.RecordingContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class RecordingContextImpl {
    constructor(_version, trunkSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(trunkSid)) {
            throw new Error("Parameter 'trunkSid' is not valid.");
        }
        this._solution = { trunkSid };
        this._uri = `/Trunks/${trunkSid}/Recording`;
    }
    fetch(callback) {
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
        });
        operationPromise = operationPromise.then((payload) => new RecordingInstance(operationVersion, payload, instance._solution.trunkSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["mode"] !== undefined)
            data["Mode"] = params["mode"];
        if (params["trim"] !== undefined)
            data["Trim"] = params["trim"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new RecordingInstance(operationVersion, payload, instance._solution.trunkSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RecordingContextImpl = RecordingContextImpl;
class RecordingInstance {
    constructor(_version, payload, trunkSid) {
        this._version = _version;
        this.mode = payload.mode;
        this.trim = payload.trim;
        this._solution = { trunkSid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new RecordingContextImpl(this._version, this._solution.trunkSid);
        return this._context;
    }
    /**
     * Fetch a RecordingInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed RecordingInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            mode: this.mode,
            trim: this.trim,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.RecordingInstance = RecordingInstance;
function RecordingListInstance(version, trunkSid) {
    if (!(0, utility_1.isValidPathParam)(trunkSid)) {
        throw new Error("Parameter 'trunkSid' is not valid.");
    }
    const instance = (() => instance.get());
    instance.get = function get() {
        return new RecordingContextImpl(version, trunkSid);
    };
    instance._version = version;
    instance._solution = { trunkSid };
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.RecordingListInstance = RecordingListInstance;
