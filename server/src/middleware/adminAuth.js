/**
 * Admin authentication middleware
 */

const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Project = require('../models/Project');
const { InMemoryUser, InMemoryProject } = require('../models/InMemoryStorage');
const config = require('../config');
const logger = require('../utils/logger');

// Helper functions to get the appropriate models
function getUserModel() {
  return global.useInMemoryStorage ? InMemoryUser : User;
}

function getProjectModel() {
  return global.useInMemoryStorage ? InMemoryProject : Project;
}

/**
 * Generate JWT token
 */
function generateToken(user) {
  return jwt.sign(
    {
      userId: user.userId,
      email: user.email,
      role: user.role
    },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );
}

/**
 * Generate session ID
 */
function generateSessionId() {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Admin login middleware
 */
const adminLogin = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // Find user by email
    const UserModel = getUserModel();
    const user = await UserModel.findByEmail(email);
    if (!user) {
      logger.warn('Login attempt with invalid email', { email, ip: req.ip });
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check if account is locked
    if (user.isLocked) {
      logger.warn('Login attempt on locked account', { userId: user.userId, ip: req.ip });
      return res.status(423).json({
        success: false,
        message: 'Account is temporarily locked due to too many failed attempts'
      });
    }

    // Verify password
    const isValidPassword = await user.comparePassword(password);
    if (!isValidPassword) {
      await user.incrementLoginAttempts();
      logger.warn('Failed login attempt', { userId: user.userId, ip: req.ip });
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Reset login attempts on successful login
    await user.resetLoginAttempts();

    // Generate token and session
    const token = generateToken(user);
    const sessionId = generateSessionId();

    // Add session to user
    await user.addSession({
      sessionId,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    logger.info('Successful admin login', { userId: user.userId, ip: req.ip });

    // Set HTTP-only cookie
    res.cookie('rum_admin_token', token, {
      httpOnly: true,
      secure: config.isProduction,
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    res.json({
      success: true,
      message: 'Login successful',
      user: {
        userId: user.userId,
        email: user.email,
        name: user.name,
        role: user.role,
        organization: user.organization
      },
      sessionId
    });

  } catch (error) {
    logger.error('Admin login error', { error: error.message, ip: req.ip });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Admin logout middleware
 */
const adminLogout = async (req, res, next) => {
  try {
    const token = req.cookies.rum_admin_token;

    if (token) {
      try {
        const decoded = jwt.verify(token, config.jwt.secret);
        const UserModel = getUserModel();
        const user = await UserModel.findByUserId(decoded.userId);

        if (user && req.body.sessionId) {
          await user.removeSession(req.body.sessionId);
        }
      } catch (error) {
        // Token invalid, continue with logout
      }
    }

    res.clearCookie('rum_admin_token');
    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    logger.error('Admin logout error', { error: error.message, ip: req.ip });
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Require admin authentication
 */
const requireAuth = async (req, res, next) => {
  try {
    const token = req.cookies.rum_admin_token || req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret);
    const UserModel = getUserModel();
    const user = await UserModel.findByUserId(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid authentication token'
      });
    }

    // Add user to request
    req.user = user;
    next();

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Authentication token expired'
      });
    }

    logger.error('Authentication error', { error: error.message, ip: req.ip });
    res.status(401).json({
      success: false,
      message: 'Invalid authentication token'
    });
  }
};

/**
 * Require specific role
 */
const requireRole = (requiredRole) => {
  const roleHierarchy = { user: 0, admin: 1, super_admin: 2 };

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userRoleLevel = roleHierarchy[req.user.role] || 0;
    const requiredRoleLevel = roleHierarchy[requiredRole] || 0;

    if (userRoleLevel < requiredRoleLevel) {
      logger.warn('Insufficient permissions', {
        userId: req.user.userId,
        userRole: req.user.role,
        requiredRole,
        ip: req.ip
      });

      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

/**
 * Require project access
 */
const requireProjectAccess = (requiredRole = 'viewer') => {
  return async (req, res, next) => {
    try {
      const projectId = req.params.projectId || req.body.projectId || req.query.projectId;

      if (!projectId) {
        return res.status(400).json({
          success: false,
          message: 'Project ID required'
        });
      }

      // Super admins have access to all projects
      if (req.user.role === 'super_admin') {
        const project = await Project.findByProjectId(projectId);
        if (!project) {
          return res.status(404).json({
            success: false,
            message: 'Project not found'
          });
        }
        req.project = project;
        return next();
      }

      // Check project-specific permissions
      const ProjectModel = getProjectModel();
      const project = await ProjectModel.findByProjectId(projectId);
      if (!project) {
        return res.status(404).json({
          success: false,
          message: 'Project not found'
        });
      }

      if (!project.hasPermission(req.user.userId, requiredRole)) {
        logger.warn('Insufficient project permissions', {
          userId: req.user.userId,
          projectId,
          requiredRole,
          ip: req.ip
        });

        return res.status(403).json({
          success: false,
          message: 'Insufficient project permissions'
        });
      }

      req.project = project;
      next();

    } catch (error) {
      logger.error('Project access check error', { error: error.message, ip: req.ip });
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  };
};

module.exports = {
  adminLogin,
  adminLogout,
  requireAuth,
  requireRole,
  requireProjectAccess,
  generateToken,
  generateSessionId
};
