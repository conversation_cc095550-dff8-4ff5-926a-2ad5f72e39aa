/**
 * In-memory storage for demo purposes (when MongoDB is not available)
 */

class InMemoryErrorLog {
  constructor() {
    this.errors = [];
    this.nextId = 1;
  }

  async save(errorData) {
    const error = {
      _id: this.nextId++,
      ...errorData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.errors.push(error);
    return error;
  }

  static async find(query = {}) {
    const storage = global.inMemoryStorage;
    if (!storage) return [];

    let results = [...storage.errors];

    // Simple query filtering
    if (query.sessionId) {
      results = results.filter(error => error.sessionId === query.sessionId);
    }

    if (query.type) {
      results = results.filter(error => error.type === query.type);
    }

    if (query.severity) {
      results = results.filter(error => error.severity === query.severity);
    }

    if (query.createdAt && query.createdAt.$gte) {
      results = results.filter(error => new Date(error.createdAt) >= query.createdAt.$gte);
    }

    return {
      sort: (sortObj) => ({
        skip: (skipNum) => ({
          limit: (limitNum) => ({
            lean: () => {
              // Simple sorting
              if (sortObj.createdAt === -1) {
                results.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
              }

              // Apply skip and limit
              return results.slice(skipNum, skipNum + limitNum);
            }
          })
        })
      })
    };
  }

  static async findOne(query) {
    const storage = global.inMemoryStorage;
    if (!storage) return null;

    if (query.errorId) {
      return storage.errors.find(error => error.errorId === query.errorId);
    }

    return storage.errors.find(error => {
      return Object.keys(query).every(key => error[key] === query[key]);
    });
  }

  static async countDocuments(query = {}) {
    const storage = global.inMemoryStorage;
    if (!storage) return 0;

    let results = [...storage.errors];

    if (query.sessionId) {
      results = results.filter(error => error.sessionId === query.sessionId);
    }

    if (query.createdAt && query.createdAt.$gte) {
      results = results.filter(error => new Date(error.createdAt) >= query.createdAt.$gte);
    }

    if (query.severity) {
      results = results.filter(error => error.severity === query.severity);
    }

    return results.length;
  }

  static async deleteMany(query) {
    const storage = global.inMemoryStorage;
    if (!storage) return { deletedCount: 0 };

    const initialLength = storage.errors.length;

    if (query.sessionId) {
      storage.errors = storage.errors.filter(error => error.sessionId !== query.sessionId);
    }

    return { deletedCount: initialLength - storage.errors.length };
  }

  static async distinct(field, query = {}) {
    const storage = global.inMemoryStorage;
    if (!storage) return [];

    let results = [...storage.errors];

    if (query.createdAt && query.createdAt.$gte) {
      results = results.filter(error => new Date(error.createdAt) >= query.createdAt.$gte);
    }

    const values = results.map(error => error[field]).filter(Boolean);
    return [...new Set(values)];
  }

  static async aggregate(pipeline) {
    const storage = global.inMemoryStorage;
    if (!storage) return [];

    // Simple aggregation for demo purposes
    let results = [...storage.errors];

    for (const stage of pipeline) {
      if (stage.$match) {
        const match = stage.$match;
        if (match.createdAt && match.createdAt.$gte) {
          results = results.filter(error => new Date(error.createdAt) >= match.createdAt.$gte);
        }
      }

      if (stage.$group) {
        const group = stage.$group;
        const grouped = {};

        results.forEach(error => {
          let key;
          if (group._id.type) {
            key = error.type;
          } else if (group._id.severity) {
            key = error.severity;
          } else if (group._id === '$sessionId') {
            key = error.sessionId;
          } else {
            key = 'all';
          }

          if (!grouped[key]) {
            grouped[key] = {
              _id: group._id.type ? { type: key } :
                group._id.severity ? { severity: key } :
                  group._id === '$sessionId' ? key : 'all',
              count: 0
            };
          }

          grouped[key].count++;
        });

        results = Object.values(grouped);
      }

      if (stage.$sort) {
        if (stage.$sort.count === -1) {
          results.sort((a, b) => b.count - a.count);
        }
      }

      if (stage.$limit) {
        results = results.slice(0, stage.$limit);
      }
    }

    return results;
  }

  // Static methods for compatibility
  static getErrorStats() {
    return this.aggregate([
      { $group: { _id: { type: '$type', severity: '$severity' }, count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
  }

  static getErrorTrends() {
    return [];
  }

  static getTopErrors(limit = 10) {
    return this.aggregate([
      { $group: { _id: { message: '$message', type: '$type' }, count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: limit }
    ]);
  }
}

class InMemoryAlertLog {
  constructor() {
    this.alerts = [];
    this.nextId = 1;
  }

  async save(alertData) {
    const alert = {
      _id: this.nextId++,
      ...alertData,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.alerts.push(alert);
    return alert;
  }

  async markAsSent() {
    this.status = 'sent';
    this.sentAt = new Date();
    return this;
  }

  async markAsFailed(reason) {
    this.status = 'failed';
    this.failureReason = reason;
    this.retryCount = (this.retryCount || 0) + 1;
    return this;
  }

  static async findOne(query) {
    const storage = global.inMemoryStorage;
    if (!storage) return null;

    return storage.alertLogs.find(alert => {
      return Object.keys(query).every(key => {
        if (key === 'createdAt' && query[key].$gte) {
          return new Date(alert.createdAt) >= query[key].$gte;
        }
        return alert[key] === query[key];
      });
    });
  }

  static async shouldSendAlert(recipient, alertType, cooldownMinutes = 30) {
    const startTime = new Date(Date.now() - cooldownMinutes * 60 * 1000);
    const alert = await this.findOne({
      recipient,
      type: alertType,
      createdAt: { $gte: startTime },
      status: { $in: ['sent', 'pending'] }
    });
    return !alert;
  }

  static async getAlertStats() {
    return [];
  }

  static async find(query = {}) {
    const storage = global.inMemoryStorage;
    if (!storage) return [];

    return storage.alertLogs.filter(alert => {
      return Object.keys(query).every(key => {
        if (key === 'createdAt' && query[key].$gte) {
          return new Date(alert.createdAt) >= query[key].$gte;
        }
        return alert[key] === query[key];
      });
    }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }
}

// In-memory User storage
class InMemoryUser {
  constructor() {
    this.users = [];
    this.nextId = 1;
  }

  async save(userData) {
    const bcrypt = require('bcryptjs');

    // Hash password if provided
    if (userData.password && !userData.password.startsWith('$2a$')) {
      const salt = await bcrypt.genSalt(12);
      userData.password = await bcrypt.hash(userData.password, salt);
    }

    const user = {
      _id: this.nextId++,
      userId: userData.userId || 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
      ...userData,
      createdAt: new Date(),
      updatedAt: new Date(),
      sessions: userData.sessions || [],
      loginAttempts: 0
    };

    this.users.push(user);
    return user;
  }

  static async findByEmail(email) {
    const storage = global.inMemoryStorage;
    if (!storage) return null;

    return storage.users.find(user =>
      user.email.toLowerCase() === email.toLowerCase() && user.status === 'active'
    );
  }

  static async findByUserId(userId) {
    const storage = global.inMemoryStorage;
    if (!storage) return null;

    return storage.users.find(user => user.userId === userId && user.status === 'active');
  }

  static async createUser(userData) {
    const storage = global.inMemoryStorage;
    if (!storage) return null;

    const inMemoryUser = new InMemoryUser();
    return await inMemoryUser.save(userData);
  }

  async comparePassword(candidatePassword) {
    const bcrypt = require('bcryptjs');
    return bcrypt.compare(candidatePassword, this.password);
  }

  async addSession(sessionData) {
    // Remove old sessions (keep only last 5)
    this.sessions = this.sessions
      .filter(session => session.expiresAt > new Date())
      .slice(-4);

    // Add new session
    this.sessions.push({
      sessionId: sessionData.sessionId,
      expiresAt: sessionData.expiresAt,
      ipAddress: sessionData.ipAddress,
      userAgent: sessionData.userAgent
    });

    this.lastLoginAt = new Date();
    return this;
  }

  async removeSession(sessionId) {
    this.sessions = this.sessions.filter(session => session.sessionId !== sessionId);
    return this;
  }

  hasValidSession(sessionId) {
    const session = this.sessions.find(s => s.sessionId === sessionId);
    return session && session.expiresAt > new Date();
  }

  async resetLoginAttempts() {
    this.loginAttempts = 0;
    this.lockedUntil = null;
    return this;
  }

  async incrementLoginAttempts() {
    this.loginAttempts += 1;
    if (this.loginAttempts >= 5) {
      this.lockedUntil = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours
    }
    return this;
  }

  get isLocked() {
    return !!(this.lockedUntil && this.lockedUntil > Date.now());
  }
}

// In-memory Project storage
class InMemoryProject {
  constructor() {
    this.projects = [];
    this.nextId = 1;
  }

  async save(projectData) {
    const project = {
      _id: this.nextId++,
      ...projectData,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.projects.push(project);
    return project;
  }

  static async findByApiKey(apiKey) {
    const storage = global.inMemoryStorage;
    if (!storage) return null;

    return storage.projects.find(project =>
      project.apiKey === apiKey && project.status === 'active'
    );
  }

  static async findByProjectId(projectId) {
    const storage = global.inMemoryStorage;
    if (!storage) return null;

    return storage.projects.find(project =>
      project.projectId === projectId && project.status === 'active'
    );
  }

  static async findByUser(userId) {
    const storage = global.inMemoryStorage;
    if (!storage) return [];

    return storage.projects.filter(project =>
      (project.owner?.userId === userId ||
        project.team?.some(member => member.userId === userId)) &&
      project.status !== 'suspended'
    );
  }

  static async find(query = {}) {
    const storage = global.inMemoryStorage;
    if (!storage) return [];

    let results = [...storage.projects];

    if (query.status && query.status.$in) {
      results = results.filter(project => query.status.$in.includes(project.status));
    }

    return results.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  }

  async incrementErrorCount() {
    this.stats = this.stats || { totalErrors: 0 };
    this.stats.totalErrors += 1;
    this.stats.lastErrorAt = new Date();
    return this;
  }

  async updateLastAlert() {
    this.stats = this.stats || {};
    this.stats.lastAlertAt = new Date();
    return this;
  }

  hasPermission(userId, requiredRole = 'viewer') {
    const roleHierarchy = { viewer: 0, developer: 1, admin: 2 };

    // Check if user is owner
    if (this.owner?.userId === userId) {
      return true;
    }

    // Check team permissions
    const teamMember = this.team?.find(member => member.userId === userId);
    if (!teamMember) {
      return false;
    }

    return roleHierarchy[teamMember.role] >= roleHierarchy[requiredRole];
  }
}

// Initialize global storage
if (!global.inMemoryStorage) {
  global.inMemoryStorage = {
    errors: [],
    alertLogs: [],
    users: [],
    projects: []
  };
}

module.exports = {
  InMemoryErrorLog,
  InMemoryAlertLog,
  InMemoryUser,
  InMemoryProject
};
