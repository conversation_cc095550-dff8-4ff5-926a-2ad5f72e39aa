/**
 * Error Log Model
 */

const mongoose = require('mongoose');

const errorLogSchema = new mongoose.Schema({
  // Error identification
  errorId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },

  batchId: {
    type: String,
    required: true,
    index: true
  },

  sessionId: {
    type: String,
    required: true,
    index: true
  },

  // Project information
  projectId: {
    type: String,
    required: true,
    index: true
  },

  // Error details
  type: {
    type: String,
    required: true,
    enum: ['javascript', 'network', 'resource', 'console', 'unhandled_rejection', 'custom'],
    index: true
  },

  message: {
    type: String,
    required: true
  },

  stack: {
    type: String
  },

  severity: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high', 'critical'],
    index: true
  },

  // Location information
  url: {
    type: String,
    required: true
  },

  filename: {
    type: String
  },

  lineno: {
    type: Number
  },

  colno: {
    type: Number
  },

  // Network error specific fields
  status: {
    type: Number
  },

  statusText: {
    type: String
  },

  method: {
    type: String
  },

  // Resource error specific fields
  resourceType: {
    type: String
  },

  resourceUrl: {
    type: String
  },

  // Environment information
  environment: {
    type: String,
    required: true,
    index: true
  },

  userAgent: {
    type: String,
    required: true
  },

  viewport: {
    width: Number,
    height: Number
  },

  screen: {
    width: Number,
    height: Number
  },

  // Timestamps
  timestamp: {
    type: Date,
    required: true,
    index: true
  },

  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },

  // Additional metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
errorLogSchema.index({ createdAt: -1 });
errorLogSchema.index({ projectId: 1, createdAt: -1 });
errorLogSchema.index({ sessionId: 1, createdAt: -1 });
errorLogSchema.index({ type: 1, severity: 1, createdAt: -1 });
errorLogSchema.index({ environment: 1, createdAt: -1 });
errorLogSchema.index({ projectId: 1, severity: 1, createdAt: -1 });

// TTL index to automatically delete old logs (optional)
// errorLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 }); // 30 days

// Static methods
errorLogSchema.statics.getErrorStats = function (timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: {
          type: '$type',
          severity: '$severity'
        },
        count: { $sum: 1 },
        latestError: { $max: '$createdAt' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

errorLogSchema.statics.getErrorTrends = function (timeRange = 24, interval = 1) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
  const intervalMs = interval * 60 * 60 * 1000;

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: {
          interval: {
            $floor: {
              $divide: [
                { $subtract: ['$createdAt', startTime] },
                intervalMs
              ]
            }
          },
          severity: '$severity'
        },
        count: { $sum: 1 },
        timestamp: {
          $first: {
            $add: [
              startTime,
              {
                $multiply: [
                  { $floor: { $divide: [{ $subtract: ['$createdAt', startTime] }, intervalMs] } },
                  intervalMs
                ]
              }
            ]
          }
        }
      }
    },
    {
      $sort: { '_id.interval': 1 }
    }
  ]);
};

errorLogSchema.statics.getTopErrors = function (limit = 10, timeRange = 24) {
  const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);

  return this.aggregate([
    {
      $match: {
        createdAt: { $gte: startTime }
      }
    },
    {
      $group: {
        _id: {
          message: '$message',
          type: '$type'
        },
        count: { $sum: 1 },
        severity: { $first: '$severity' },
        latestOccurrence: { $max: '$createdAt' },
        affectedSessions: { $addToSet: '$sessionId' }
      }
    },
    {
      $addFields: {
        sessionCount: { $size: '$affectedSessions' }
      }
    },
    {
      $sort: { count: -1 }
    },
    {
      $limit: limit
    }
  ]);
};

const ErrorLog = mongoose.model('ErrorLog', errorLogSchema);

module.exports = ErrorLog;
