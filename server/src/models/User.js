/**
 * User Model for admin authentication
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // User identification
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    index: true
  },
  
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  
  // User details
  name: {
    type: String,
    required: true
  },
  
  role: {
    type: String,
    enum: ['super_admin', 'admin', 'user'],
    default: 'user',
    index: true
  },
  
  // Organization info
  organization: {
    type: String
  },
  
  department: {
    type: String
  },
  
  // User status
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active',
    index: true
  },
  
  // Authentication
  lastLoginAt: {
    type: Date
  },
  
  loginAttempts: {
    type: Number,
    default: 0
  },
  
  lockedUntil: {
    type: Date
  },
  
  // Session management
  sessions: [{
    sessionId: String,
    createdAt: {
      type: Date,
      default: Date.now
    },
    expiresAt: Date,
    ipAddress: String,
    userAgent: String
  }],
  
  // Preferences
  preferences: {
    emailNotifications: {
      type: Boolean,
      default: true
    },
    
    smsNotifications: {
      type: Boolean,
      default: false
    },
    
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light'
    }
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes
userSchema.index({ role: 1, status: 1 });
userSchema.index({ organization: 1, status: 1 });

// Virtual for account lock status
userSchema.virtual('isLocked').get(function() {
  return !!(this.lockedUntil && this.lockedUntil > Date.now());
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.incrementLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockedUntil && this.lockedUntil < Date.now()) {
    return this.updateOne({
      $unset: { lockedUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates = { $inc: { loginAttempts: 1 } };
  
  // If we're at max attempts and not locked, lock account
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockedUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockedUntil: 1 }
  });
};

userSchema.methods.addSession = function(sessionData) {
  // Remove old sessions (keep only last 5)
  this.sessions = this.sessions
    .filter(session => session.expiresAt > new Date())
    .slice(-4);
  
  // Add new session
  this.sessions.push({
    sessionId: sessionData.sessionId,
    expiresAt: sessionData.expiresAt,
    ipAddress: sessionData.ipAddress,
    userAgent: sessionData.userAgent
  });
  
  this.lastLoginAt = new Date();
  return this.save();
};

userSchema.methods.removeSession = function(sessionId) {
  this.sessions = this.sessions.filter(session => session.sessionId !== sessionId);
  return this.save();
};

userSchema.methods.hasValidSession = function(sessionId) {
  const session = this.sessions.find(s => s.sessionId === sessionId);
  return session && session.expiresAt > new Date();
};

// Static methods
userSchema.statics.findByEmail = function(email) {
  return this.findOne({ email: email.toLowerCase(), status: 'active' });
};

userSchema.statics.findByUserId = function(userId) {
  return this.findOne({ userId, status: 'active' });
};

userSchema.statics.createUser = async function(userData) {
  const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  
  return this.create({
    userId,
    ...userData
  });
};

const User = mongoose.model('User', userSchema);

module.exports = User;
