/**
 * Project Model for multi-project RUM system
 */

const mongoose = require('mongoose');

const projectSchema = new mongoose.Schema({
  // Project identification
  projectId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  name: {
    type: String,
    required: true
  },
  
  description: {
    type: String
  },
  
  // Project configuration
  domain: {
    type: String,
    required: true
  },
  
  allowedOrigins: [{
    type: String
  }],
  
  // Alert configuration
  alertConfig: {
    enabled: {
      type: Boolean,
      default: true
    },
    
    errorThreshold: {
      type: Number,
      default: 10
    },
    
    timeWindowMinutes: {
      type: Number,
      default: 5
    },
    
    // Project-specific alert recipients
    emailRecipients: [{
      email: String,
      name: String,
      role: String
    }],
    
    smsRecipients: [{
      phone: String,
      name: String,
      role: String
    }],
    
    // Alert cooldown periods
    emailCooldownMinutes: {
      type: Number,
      default: 30
    },
    
    smsCooldownMinutes: {
      type: Number,
      default: 60
    }
  },
  
  // RUM configuration
  rumConfig: {
    enableLogging: {
      type: <PERSON>olean,
      default: true
    },
    
    enableConsoleCapture: {
      type: <PERSON>olean,
      default: true
    },
    
    enableNetworkCapture: {
      type: Boolean,
      default: true
    },
    
    enableResourceCapture: {
      type: Boolean,
      default: true
    },
    
    maxErrorsPerSession: {
      type: Number,
      default: 100
    },
    
    batchSize: {
      type: Number,
      default: 10
    },
    
    batchTimeout: {
      type: Number,
      default: 5000
    }
  },
  
  // Project status
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active',
    index: true
  },
  
  // API key for this project
  apiKey: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Project owner/team
  owner: {
    userId: String,
    name: String,
    email: String
  },
  
  team: [{
    userId: String,
    name: String,
    email: String,
    role: {
      type: String,
      enum: ['admin', 'developer', 'viewer'],
      default: 'viewer'
    }
  }],
  
  // Statistics
  stats: {
    totalErrors: {
      type: Number,
      default: 0
    },
    
    lastErrorAt: {
      type: Date
    },
    
    lastAlertAt: {
      type: Date
    }
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes
projectSchema.index({ status: 1, createdAt: -1 });
projectSchema.index({ 'owner.userId': 1 });
projectSchema.index({ 'team.userId': 1 });

// Static methods
projectSchema.statics.findByApiKey = function(apiKey) {
  return this.findOne({ apiKey, status: 'active' });
};

projectSchema.statics.findByProjectId = function(projectId) {
  return this.findOne({ projectId, status: 'active' });
};

projectSchema.statics.findByUser = function(userId) {
  return this.find({
    $or: [
      { 'owner.userId': userId },
      { 'team.userId': userId }
    ],
    status: { $in: ['active', 'inactive'] }
  });
};

// Instance methods
projectSchema.methods.incrementErrorCount = function() {
  this.stats.totalErrors += 1;
  this.stats.lastErrorAt = new Date();
  return this.save();
};

projectSchema.methods.updateLastAlert = function() {
  this.stats.lastAlertAt = new Date();
  return this.save();
};

projectSchema.methods.hasPermission = function(userId, requiredRole = 'viewer') {
  const roleHierarchy = { viewer: 0, developer: 1, admin: 2 };
  
  // Check if user is owner
  if (this.owner.userId === userId) {
    return true;
  }
  
  // Check team permissions
  const teamMember = this.team.find(member => member.userId === userId);
  if (!teamMember) {
    return false;
  }
  
  return roleHierarchy[teamMember.role] >= roleHierarchy[requiredRole];
};

const Project = mongoose.model('Project', projectSchema);

module.exports = Project;
