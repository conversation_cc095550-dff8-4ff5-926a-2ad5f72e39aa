/**
 * RUM Error Logging Server
 */

const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');

const config = require('./config');
const logger = require('./utils/logger');

// Middleware
const { optionalApiKey } = require('./middleware/auth');
const { skipRateLimit } = require('./middleware/rateLimiter');
const { notFoundHandler, errorHandler } = require('./middleware/errorHandler');

// Routes
const errorRoutes = require('./routes/errors');
const statsRoutes = require('./routes/stats');

// Create Express app
const app = express();

// Trust proxy (for rate limiting and IP detection)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors(config.cors));

// Compression middleware
app.use(compression());

// Request logging
app.use(morgan('combined', { stream: logger.stream }));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
app.use(skipRateLimit);

// Health check endpoint (no auth required)
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: config.nodeEnv
  });
});

// API routes with optional authentication
app.use('/api/errors', optionalApiKey, errorRoutes);
app.use('/api/stats', optionalApiKey, statsRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'RUM Error Logging Server',
    version: '1.0.0',
    environment: config.nodeEnv,
    endpoints: {
      health: '/health',
      errors: '/api/errors',
      stats: '/api/stats'
    }
  });
});

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

/**
 * Connect to MongoDB
 */
async function connectDatabase() {
  try {
    await mongoose.connect(config.mongodb.uri, config.mongodb.options);
    logger.info('Connected to MongoDB', {
      database: config.mongodb.dbName,
      host: config.mongodb.uri.split('@')[1] || 'localhost'
    });
    global.useInMemoryStorage = false;
  } catch (error) {
    logger.warn('MongoDB connection failed, using in-memory storage for demo', {
      error: error.message
    });
    global.useInMemoryStorage = true;

    // Initialize in-memory storage
    if (!global.inMemoryStorage) {
      global.inMemoryStorage = {
        errors: [],
        alertLogs: []
      };
    }
  }
}

/**
 * Start the server
 */
async function startServer() {
  try {
    // Connect to database
    await connectDatabase();

    // Start listening
    const server = app.listen(config.port, () => {
      logger.info('Server started', {
        port: config.port,
        environment: config.nodeEnv,
        pid: process.pid
      });
    });

    // Graceful shutdown handling
    const gracefulShutdown = (signal) => {
      logger.info(`Received ${signal}, shutting down gracefully`);

      server.close(() => {
        logger.info('HTTP server closed');

        mongoose.connection.close(false, () => {
          logger.info('MongoDB connection closed');
          process.exit(0);
        });
      });

      // Force close after 10 seconds
      setTimeout(() => {
        logger.error('Could not close connections in time, forcefully shutting down');
        process.exit(1);
      }, 10000);
    };

    // Listen for termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    return server;
  } catch (error) {
    logger.error('Failed to start server', {
      error: error.message,
      stack: error.stack
    });
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', {
    error: error.message,
    stack: error.stack
  });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', {
    reason: reason.toString(),
    promise: promise.toString()
  });
  process.exit(1);
});

// Start the server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = { app, startServer };
