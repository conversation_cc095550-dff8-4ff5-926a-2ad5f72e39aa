/**
 * Admin Dashboard HTML Template
 */

function getLoginPage() {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>RUM Admin Login</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .login-container {
                background: white;
                padding: 40px;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                width: 100%;
                max-width: 400px;
            }
            .logo {
                text-align: center;
                margin-bottom: 30px;
            }
            .logo h1 {
                color: #333;
                font-size: 2em;
                margin-bottom: 10px;
            }
            .logo p {
                color: #666;
                font-size: 0.9em;
            }
            .form-group {
                margin-bottom: 20px;
            }
            .form-group label {
                display: block;
                margin-bottom: 5px;
                color: #333;
                font-weight: 500;
            }
            .form-group input {
                width: 100%;
                padding: 12px;
                border: 2px solid #e1e5e9;
                border-radius: 6px;
                font-size: 16px;
                transition: border-color 0.3s;
            }
            .form-group input:focus {
                outline: none;
                border-color: #667eea;
            }
            .btn {
                width: 100%;
                background: #667eea;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 6px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            .btn:hover {
                background: #5a6fd8;
            }
            .btn:disabled {
                background: #ccc;
                cursor: not-allowed;
            }
            .error {
                background: #fee;
                color: #c33;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 20px;
                display: none;
            }
            .loading {
                display: none;
                text-align: center;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="logo">
                <h1>🚨 RUM Admin</h1>
                <p>Real User Monitoring System</p>
            </div>
            
            <div class="error" id="error-message"></div>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                
                <button type="submit" class="btn" id="login-btn">
                    Sign In
                </button>
            </form>
            
            <div class="loading" id="loading">
                <p>Signing in...</p>
            </div>
        </div>

        <script>
            document.getElementById('login-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const errorDiv = document.getElementById('error-message');
                const loadingDiv = document.getElementById('loading');
                const loginBtn = document.getElementById('login-btn');
                
                // Reset error state
                errorDiv.style.display = 'none';
                loadingDiv.style.display = 'block';
                loginBtn.disabled = true;
                
                try {
                    const response = await fetch('/admin/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // Store session info
                        localStorage.setItem('rum_session', JSON.stringify({
                            sessionId: data.sessionId,
                            user: data.user
                        }));
                        
                        // Redirect to dashboard
                        window.location.href = '/admin/dashboard';
                    } else {
                        errorDiv.textContent = data.message;
                        errorDiv.style.display = 'block';
                    }
                } catch (error) {
                    errorDiv.textContent = 'Login failed. Please try again.';
                    errorDiv.style.display = 'block';
                } finally {
                    loadingDiv.style.display = 'none';
                    loginBtn.disabled = false;
                }
            });
        </script>
    </body>
    </html>
  `;
}

function getDashboardPage(user) {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>RUM Admin Dashboard</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
            .header { background: white; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
            .header-content { max-width: 1200px; margin: 0 auto; display: flex; justify-content: space-between; align-items: center; }
            .header h1 { color: #333; }
            .user-info { display: flex; align-items: center; gap: 15px; }
            .user-info span { color: #666; }
            .btn { background: #2563eb; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
            .btn:hover { background: #1d4ed8; }
            .btn-danger { background: #dc2626; }
            .btn-danger:hover { background: #b91c1c; }
            .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
            .nav-tabs { display: flex; gap: 10px; margin-bottom: 20px; }
            .nav-tab { background: white; border: none; padding: 12px 24px; border-radius: 6px 6px 0 0; cursor: pointer; border-bottom: 3px solid transparent; }
            .nav-tab.active { border-bottom-color: #2563eb; background: #f8fafc; }
            .tab-content { display: none; }
            .tab-content.active { display: block; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
            .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .stat-number { font-size: 2em; font-weight: bold; color: #2563eb; }
            .stat-label { color: #666; margin-top: 5px; }
            .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .section h2 { margin-bottom: 15px; color: #333; }
            .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
            .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
            .table th { background: #f9fafb; font-weight: 600; }
            .status { padding: 4px 8px; border-radius: 4px; font-size: 0.875em; }
            .status-active { background: #dcfce7; color: #166534; }
            .status-inactive { background: #fef2f2; color: #dc2626; }
            .project-card { background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; margin-bottom: 15px; }
            .project-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }
            .project-name { font-size: 1.2em; font-weight: 600; color: #333; }
            .project-domain { color: #666; font-size: 0.9em; }
            .project-stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-top: 15px; }
            .project-stat { text-align: center; }
            .project-stat-number { font-size: 1.5em; font-weight: bold; color: #2563eb; }
            .project-stat-label { font-size: 0.8em; color: #666; }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="header-content">
                <h1>🚨 RUM Admin Dashboard</h1>
                <div class="user-info">
                    <span>Welcome, ${user.name}</span>
                    <span class="status status-active">${user.role}</span>
                    <button class="btn btn-danger" onclick="logout()">Logout</button>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('overview')">Overview</button>
                <button class="nav-tab" onclick="showTab('projects')">Projects</button>
                <button class="nav-tab" onclick="showTab('users')">Users</button>
                <button class="nav-tab" onclick="showTab('settings')">Settings</button>
            </div>

            <!-- Overview Tab -->
            <div id="overview-tab" class="tab-content active">
                <div class="stats-grid" id="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="total-projects">Loading...</div>
                        <div class="stat-label">Total Projects</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="total-errors">Loading...</div>
                        <div class="stat-label">Total Errors (24h)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="critical-errors">Loading...</div>
                        <div class="stat-label">Critical Errors (24h)</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="active-sessions">Loading...</div>
                        <div class="stat-label">Active Sessions (1h)</div>
                    </div>
                </div>

                <div class="section">
                    <h2>📊 Recent Errors</h2>
                    <table class="table" id="recent-errors">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Project</th>
                                <th>Type</th>
                                <th>Severity</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr><td colspan="5">Loading...</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Projects Tab -->
            <div id="projects-tab" class="tab-content">
                <div class="section">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>🏗️ Projects</h2>
                        <button class="btn" onclick="showCreateProject()">+ Create Project</button>
                    </div>
                    <div id="projects-list">Loading...</div>
                </div>
            </div>

            <!-- Users Tab -->
            <div id="users-tab" class="tab-content">
                <div class="section">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>👥 Users</h2>
                        <button class="btn" onclick="showCreateUser()">+ Create User</button>
                    </div>
                    <div id="users-list">Loading...</div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings-tab" class="tab-content">
                <div class="section">
                    <h2>⚙️ System Settings</h2>
                    <div style="display: grid; gap: 10px;">
                        <button class="btn" onclick="exportAllData()">📊 Export All Data</button>
                        <button class="btn btn-danger" onclick="clearAllData()">🗑️ Clear All Data</button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Auto-refresh data every 30 seconds
            setInterval(loadDashboardData, 30000);
            
            // Load initial data
            loadDashboardData();

            function showTab(tabName) {
                // Hide all tabs
                document.querySelectorAll('.tab-content').forEach(tab => {
                    tab.classList.remove('active');
                });
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // Show selected tab
                document.getElementById(tabName + '-tab').classList.add('active');
                event.target.classList.add('active');
                
                // Load tab-specific data
                if (tabName === 'projects') loadProjects();
                if (tabName === 'users') loadUsers();
            }

            async function loadDashboardData() {
                try {
                    const [overviewRes, healthRes] = await Promise.all([
                        fetch('/api/stats/overview'),
                        fetch('/api/stats/health')
                    ]);
                    
                    const overview = await overviewRes.json();
                    const health = await healthRes.json();
                    
                    document.getElementById('total-errors').textContent = overview.data.overview.totalErrors;
                    document.getElementById('critical-errors').textContent = overview.data.overview.criticalErrors;
                    document.getElementById('active-sessions').textContent = overview.data.overview.uniqueSessions;
                    
                    // Load projects count
                    const projectsRes = await fetch('/admin/api/projects');
                    const projects = await projectsRes.json();
                    document.getElementById('total-projects').textContent = projects.data?.length || 0;
                    
                } catch (error) {
                    console.error('Failed to load dashboard data:', error);
                }
            }

            async function loadProjects() {
                try {
                    const response = await fetch('/admin/api/projects');
                    const data = await response.json();
                    
                    const container = document.getElementById('projects-list');
                    container.innerHTML = '';
                    
                    if (data.data && data.data.length > 0) {
                        data.data.forEach(project => {
                            const projectCard = createProjectCard(project);
                            container.appendChild(projectCard);
                        });
                    } else {
                        container.innerHTML = '<p>No projects found. Create your first project to get started.</p>';
                    }
                } catch (error) {
                    console.error('Failed to load projects:', error);
                }
            }

            function createProjectCard(project) {
                const card = document.createElement('div');
                card.className = 'project-card';
                card.innerHTML = \`
                    <div class="project-header">
                        <div>
                            <div class="project-name">\${project.name}</div>
                            <div class="project-domain">\${project.domain}</div>
                        </div>
                        <div>
                            <span class="status status-\${project.status}">\${project.status}</span>
                        </div>
                    </div>
                    <div class="project-stats">
                        <div class="project-stat">
                            <div class="project-stat-number">\${project.stats?.totalErrors || 0}</div>
                            <div class="project-stat-label">Total Errors</div>
                        </div>
                        <div class="project-stat">
                            <div class="project-stat-number">\${project.alertConfig?.errorThreshold || 0}</div>
                            <div class="project-stat-label">Alert Threshold</div>
                        </div>
                        <div class="project-stat">
                            <div class="project-stat-number">\${project.alertConfig?.emailRecipients?.length || 0}</div>
                            <div class="project-stat-label">Alert Recipients</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <button class="btn" onclick="editProject('\${project.projectId}')">Edit</button>
                        <button class="btn" onclick="viewProjectErrors('\${project.projectId}')">View Errors</button>
                    </div>
                \`;
                return card;
            }

            async function logout() {
                try {
                    const session = JSON.parse(localStorage.getItem('rum_session') || '{}');
                    
                    await fetch('/admin/logout', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ sessionId: session.sessionId })
                    });
                    
                    localStorage.removeItem('rum_session');
                    window.location.href = '/admin';
                } catch (error) {
                    console.error('Logout failed:', error);
                    // Force redirect anyway
                    localStorage.removeItem('rum_session');
                    window.location.href = '/admin';
                }
            }

            // Placeholder functions for future implementation
            function loadUsers() { console.log('Loading users...'); }
            function showCreateProject() { console.log('Show create project modal'); }
            function showCreateUser() { console.log('Show create user modal'); }
            function editProject(id) { console.log('Edit project:', id); }
            function viewProjectErrors(id) { console.log('View project errors:', id); }
            function exportAllData() { console.log('Export all data'); }
            function clearAllData() { console.log('Clear all data'); }
        </script>
    </body>
    </html>
  `;
}

module.exports = {
  getLoginPage,
  getDashboardPage
};
