/**
 * Admin routes for RUM system management
 */

const express = require('express');
const router = express.Router();
const path = require('path');

const ErrorLog = require('../models/ErrorLog');
const AlertLog = require('../models/AlertLog');
const Project = require('../models/Project');
const User = require('../models/User');
const { InMemoryErrorLog, InMemoryAlertLog, InMemoryUser, InMemoryProject } = require('../models/InMemoryStorage');
const { adminRateLimit } = require('../middleware/rateLimiter');
const { asyncHandler } = require('../middleware/errorHandler');
const { adminLogin, adminLogout, requireAuth, requireRole, requireProjectAccess } = require('../middleware/adminAuth');
const { getLoginPage, getDashboardPage } = require('../views/adminDashboard');
const logger = require('../utils/logger');

// Helper functions to get the appropriate models
function getErrorModel() {
  return global.useInMemoryStorage ? InMemoryErrorLog : ErrorLog;
}

function getAlertModel() {
  return global.useInMemoryStorage ? InMemoryAlertLog : AlertLog;
}

function getUserModel() {
  return global.useInMemoryStorage ? InMemoryUser : User;
}

function getProjectModel() {
  return global.useInMemoryStorage ? InMemoryProject : Project;
}

/**
 * POST /admin/login
 * Admin login
 */
router.post('/login', adminRateLimit, adminLogin);

/**
 * POST /admin/logout
 * Admin logout
 */
router.post('/logout', adminLogout);

/**
 * GET /admin
 * Serve admin login page
 */
router.get('/', (req, res) => {
  res.send(getLoginPage());
});

/**
 * GET /admin/dashboard
 * Serve admin dashboard (requires authentication)
 */
router.get('/dashboard', requireAuth, (req, res) => {
  res.send(getDashboardPage(req.user));
});

/**
 * GET /admin/api/projects
 * Get all projects for current user
 */
router.get('/api/projects',
  requireAuth,
  asyncHandler(async (req, res) => {
    const ProjectModel = getProjectModel();
    let projects;

    if (req.user.role === 'super_admin') {
      // Super admins can see all projects
      projects = await ProjectModel.find({ status: { $in: ['active', 'inactive'] } });
    } else {
      // Regular users can only see their projects
      projects = await ProjectModel.findByUser(req.user.userId);
    }

    res.json({
      success: true,
      data: projects
    });
  })
);

/**
 * POST /admin/api/projects
 * Create a new project
 */
router.post('/api/projects',
  requireAuth,
  requireRole('admin'),
  asyncHandler(async (req, res) => {
    const { name, description, domain, alertConfig } = req.body;

    // Generate project ID and API key
    const projectId = 'proj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const apiKey = 'rum_' + Date.now() + '_' + Math.random().toString(36).substr(2, 16);

    const ProjectModel = getProjectModel();
    let project;

    if (global.useInMemoryStorage) {
      const inMemoryProject = new InMemoryProject();
      project = await inMemoryProject.save({
        projectId,
        name,
        description,
        domain,
        apiKey,
        status: 'active',
        owner: {
          userId: req.user.userId,
          name: req.user.name,
          email: req.user.email
        },
        alertConfig: {
          ...alertConfig,
          enabled: true
        },
        stats: {
          totalErrors: 0
        }
      });
    } else {
      project = new Project({
        projectId,
        name,
        description,
        domain,
        apiKey,
        owner: {
          userId: req.user.userId,
          name: req.user.name,
          email: req.user.email
        },
        alertConfig: {
          ...alertConfig,
          enabled: true
        }
      });

      await project.save();
    }

    logger.info('Project created', {
      projectId,
      name,
      createdBy: req.user.userId
    });

    res.status(201).json({
      success: true,
      message: 'Project created successfully',
      data: project
    });
  })
);



/**
 * POST /admin/clear-errors
 * Clear all errors from storage
 */
router.post('/clear-errors',
  requireAuth,
  requireRole('admin'),
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const ErrorModel = getErrorModel();

    if (global.useInMemoryStorage) {
      const initialCount = global.inMemoryStorage.errors.length;
      global.inMemoryStorage.errors = [];

      logger.info('Cleared all errors from in-memory storage', {
        clearedCount: initialCount,
        ip: req.ip
      });

      res.json({
        success: true,
        message: `Cleared ${initialCount} errors from storage`
      });
    } else {
      const result = await ErrorModel.deleteMany({});

      logger.info('Cleared all errors from database', {
        deletedCount: result.deletedCount,
        ip: req.ip
      });

      res.json({
        success: true,
        message: `Cleared ${result.deletedCount} errors from database`
      });
    }
  })
);

/**
 * POST /admin/test-alert
 * Send a test alert
 */
router.post('/test-alert',
  requireAuth,
  requireRole('admin'),
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const alertService = require('../services/alertService');

    try {
      await alertService.sendAlert({
        sessionId: 'admin-test',
        errorCount: 1,
        criticalCount: 1,
        timeWindow: 5,
        errors: [{
          errorId: 'admin-test-error',
          sessionId: 'admin-test',
          message: 'This is a test alert from the admin dashboard',
          timestamp: new Date().toISOString(),
          type: 'custom',
          severity: 'high'
        }]
      });

      logger.info('Test alert sent from admin dashboard', { ip: req.ip });

      res.json({
        success: true,
        message: 'Test alert sent successfully'
      });
    } catch (error) {
      logger.error('Failed to send test alert', { error: error.message, ip: req.ip });

      res.status(500).json({
        success: false,
        message: 'Failed to send test alert: ' + error.message
      });
    }
  })
);

/**
 * GET /admin/export
 * Export all data as JSON
 */
router.get('/export',
  requireAuth,
  requireRole('admin'),
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const ErrorModel = getErrorModel();
    const AlertModel = getAlertModel();

    try {
      const [errors, alerts] = await Promise.all([
        ErrorModel.find({}).lean(),
        AlertModel.find({}).lean()
      ]);

      const exportData = {
        exportDate: new Date().toISOString(),
        totalErrors: errors.length,
        totalAlerts: alerts.length,
        errors,
        alerts
      };

      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=rum-data-export.json');
      res.json(exportData);

      logger.info('Data export completed', {
        errorCount: errors.length,
        alertCount: alerts.length,
        ip: req.ip
      });
    } catch (error) {
      logger.error('Failed to export data', { error: error.message, ip: req.ip });
      res.status(500).json({
        success: false,
        message: 'Failed to export data: ' + error.message
      });
    }
  })
);

/**
 * GET /admin/logs
 * Get recent system logs
 */
router.get('/logs',
  requireAuth,
  requireRole('admin'),
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const fs = require('fs');
    const config = require('../config');

    try {
      if (fs.existsSync(config.logging.file)) {
        const logs = fs.readFileSync(config.logging.file, 'utf8');
        const recentLogs = logs.split('\n').slice(-50).join('\n');
        res.type('text/plain').send(recentLogs);
      } else {
        res.type('text/plain').send('No log file found. Logs are being written to console only.');
      }
    } catch (error) {
      res.type('text/plain').send('Failed to read log file: ' + error.message);
    }
  })
);

/**
 * POST /admin/restart
 * Restart the server (graceful shutdown)
 */
router.post('/restart',
  requireAuth,
  requireRole('super_admin'),
  adminRateLimit,
  asyncHandler(async (req, res) => {
    logger.info('Server restart requested from admin dashboard', { ip: req.ip });

    res.json({
      success: true,
      message: 'Server restart initiated'
    });

    // Graceful shutdown after sending response
    setTimeout(() => {
      process.kill(process.pid, 'SIGTERM');
    }, 1000);
  })
);

module.exports = router;
