/**
 * Admin routes for RUM system management
 */

const express = require('express');
const router = express.Router();
const path = require('path');

const ErrorLog = require('../models/ErrorLog');
const AlertLog = require('../models/AlertLog');
const { InMemoryErrorLog, InMemoryAlertLog } = require('../models/InMemoryStorage');
const { adminRateLimit } = require('../middleware/rateLimiter');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Helper functions to get the appropriate models
function getErrorModel() {
  return global.useInMemoryStorage ? InMemoryErrorLog : ErrorLog;
}

function getAlertModel() {
  return global.useInMemoryStorage ? InMemoryAlertLog : AlertLog;
}

/**
 * GET /admin
 * Serve admin dashboard
 */
router.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>RUM Admin Dashboard</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .header h1 { color: #333; margin-bottom: 10px; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 20px; }
            .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .stat-number { font-size: 2em; font-weight: bold; color: #2563eb; }
            .stat-label { color: #666; margin-top: 5px; }
            .section { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .section h2 { margin-bottom: 15px; color: #333; }
            .btn { background: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-right: 10px; margin-bottom: 10px; }
            .btn:hover { background: #1d4ed8; }
            .btn-danger { background: #dc2626; }
            .btn-danger:hover { background: #b91c1c; }
            .table { width: 100%; border-collapse: collapse; margin-top: 15px; }
            .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #e5e7eb; }
            .table th { background: #f9fafb; font-weight: 600; }
            .status { padding: 4px 8px; border-radius: 4px; font-size: 0.875em; }
            .status-running { background: #dcfce7; color: #166534; }
            .status-error { background: #fef2f2; color: #dc2626; }
            .log-container { background: #1f2937; color: #f9fafb; padding: 15px; border-radius: 4px; font-family: monospace; max-height: 400px; overflow-y: auto; }
            .refresh-btn { float: right; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚨 RUM Admin Dashboard</h1>
                <p>Real User Monitoring System Administration</p>
                <button class="btn refresh-btn" onclick="location.reload()">🔄 Refresh</button>
            </div>

            <div class="stats-grid" id="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-errors">Loading...</div>
                    <div class="stat-label">Total Errors (24h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="critical-errors">Loading...</div>
                    <div class="stat-label">Critical Errors (24h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-sessions">Loading...</div>
                    <div class="stat-label">Active Sessions (1h)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="health-score">Loading...</div>
                    <div class="stat-label">System Health Score</div>
                </div>
            </div>

            <div class="section">
                <h2>🎛️ System Controls</h2>
                <button class="btn" onclick="clearAllErrors()">🗑️ Clear All Errors</button>
                <button class="btn" onclick="testAlert()">📧 Test Alert</button>
                <button class="btn" onclick="exportData()">📊 Export Data</button>
                <button class="btn btn-danger" onclick="restartServer()">🔄 Restart Server</button>
            </div>

            <div class="section">
                <h2>📊 Recent Errors</h2>
                <table class="table" id="recent-errors">
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Type</th>
                            <th>Severity</th>
                            <th>Message</th>
                            <th>Session</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td colspan="5">Loading...</td></tr>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h2>🔔 Alert Status</h2>
                <div id="alert-status">
                    <div class="status status-running">Email Service: ${process.env.SMTP_USER ? 'Configured' : 'Not Configured'}</div>
                    <div class="status status-running">SMS Service: ${process.env.TWILIO_ACCOUNT_SID ? 'Configured' : 'Not Configured'}</div>
                    <div class="status status-running">Storage: ${global.useInMemoryStorage ? 'In-Memory' : 'MongoDB'}</div>
                </div>
            </div>

            <div class="section">
                <h2>📝 System Logs</h2>
                <div class="log-container" id="system-logs">
                    Loading logs...
                </div>
            </div>
        </div>

        <script>
            // Auto-refresh data every 30 seconds
            setInterval(loadDashboardData, 30000);
            
            // Load initial data
            loadDashboardData();
            loadRecentErrors();
            loadSystemLogs();

            async function loadDashboardData() {
                try {
                    const [overviewRes, healthRes] = await Promise.all([
                        fetch('/api/stats/overview'),
                        fetch('/api/stats/health')
                    ]);
                    
                    const overview = await overviewRes.json();
                    const health = await healthRes.json();
                    
                    document.getElementById('total-errors').textContent = overview.data.overview.totalErrors;
                    document.getElementById('critical-errors').textContent = overview.data.overview.criticalErrors;
                    document.getElementById('active-sessions').textContent = overview.data.overview.uniqueSessions;
                    document.getElementById('health-score').textContent = health.data.healthScore + '%';
                } catch (error) {
                    console.error('Failed to load dashboard data:', error);
                }
            }

            async function loadRecentErrors() {
                try {
                    const response = await fetch('/api/errors?limit=10&sortBy=createdAt&sortOrder=desc');
                    const data = await response.json();
                    
                    const tbody = document.querySelector('#recent-errors tbody');
                    tbody.innerHTML = '';
                    
                    if (data.data.errors.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="5">No errors found</td></tr>';
                        return;
                    }
                    
                    data.data.errors.forEach(error => {
                        const row = document.createElement('tr');
                        row.innerHTML = \`
                            <td>\${new Date(error.createdAt || error.timestamp).toLocaleString()}</td>
                            <td>\${error.type}</td>
                            <td><span class="status status-\${error.severity === 'critical' ? 'error' : 'running'}">\${error.severity}</span></td>
                            <td>\${error.message.substring(0, 50)}...</td>
                            <td>\${error.sessionId.substring(0, 8)}...</td>
                        \`;
                        tbody.appendChild(row);
                    });
                } catch (error) {
                    console.error('Failed to load recent errors:', error);
                }
            }

            async function loadSystemLogs() {
                try {
                    const response = await fetch('/admin/logs');
                    const logs = await response.text();
                    document.getElementById('system-logs').textContent = logs;
                } catch (error) {
                    document.getElementById('system-logs').textContent = 'Failed to load logs: ' + error.message;
                }
            }

            async function clearAllErrors() {
                if (confirm('Are you sure you want to clear all errors? This action cannot be undone.')) {
                    try {
                        const response = await fetch('/admin/clear-errors', { method: 'POST' });
                        const result = await response.json();
                        alert(result.message);
                        loadDashboardData();
                        loadRecentErrors();
                    } catch (error) {
                        alert('Failed to clear errors: ' + error.message);
                    }
                }
            }

            async function testAlert() {
                try {
                    const response = await fetch('/admin/test-alert', { method: 'POST' });
                    const result = await response.json();
                    alert(result.message);
                } catch (error) {
                    alert('Failed to send test alert: ' + error.message);
                }
            }

            async function exportData() {
                try {
                    const response = await fetch('/admin/export');
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'rum-data-export.json';
                    a.click();
                    window.URL.revokeObjectURL(url);
                } catch (error) {
                    alert('Failed to export data: ' + error.message);
                }
            }

            async function restartServer() {
                if (confirm('Are you sure you want to restart the server? This will cause a brief downtime.')) {
                    try {
                        await fetch('/admin/restart', { method: 'POST' });
                        alert('Server restart initiated. Please refresh the page in a few seconds.');
                    } catch (error) {
                        alert('Failed to restart server: ' + error.message);
                    }
                }
            }
        </script>
    </body>
    </html>
  `);
});

/**
 * POST /admin/clear-errors
 * Clear all errors from storage
 */
router.post('/clear-errors',
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const ErrorModel = getErrorModel();

    if (global.useInMemoryStorage) {
      const initialCount = global.inMemoryStorage.errors.length;
      global.inMemoryStorage.errors = [];

      logger.info('Cleared all errors from in-memory storage', {
        clearedCount: initialCount,
        ip: req.ip
      });

      res.json({
        success: true,
        message: `Cleared ${initialCount} errors from storage`
      });
    } else {
      const result = await ErrorModel.deleteMany({});

      logger.info('Cleared all errors from database', {
        deletedCount: result.deletedCount,
        ip: req.ip
      });

      res.json({
        success: true,
        message: `Cleared ${result.deletedCount} errors from database`
      });
    }
  })
);

/**
 * POST /admin/test-alert
 * Send a test alert
 */
router.post('/test-alert',
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const alertService = require('../services/alertService');

    try {
      await alertService.sendAlert({
        sessionId: 'admin-test',
        errorCount: 1,
        criticalCount: 1,
        timeWindow: 5,
        errors: [{
          errorId: 'admin-test-error',
          sessionId: 'admin-test',
          message: 'This is a test alert from the admin dashboard',
          timestamp: new Date().toISOString(),
          type: 'custom',
          severity: 'high'
        }]
      });

      logger.info('Test alert sent from admin dashboard', { ip: req.ip });

      res.json({
        success: true,
        message: 'Test alert sent successfully'
      });
    } catch (error) {
      logger.error('Failed to send test alert', { error: error.message, ip: req.ip });

      res.status(500).json({
        success: false,
        message: 'Failed to send test alert: ' + error.message
      });
    }
  })
);

/**
 * GET /admin/export
 * Export all data as JSON
 */
router.get('/export',
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const ErrorModel = getErrorModel();
    const AlertModel = getAlertModel();

    try {
      const [errors, alerts] = await Promise.all([
        ErrorModel.find({}).lean(),
        AlertModel.find({}).lean()
      ]);

      const exportData = {
        exportDate: new Date().toISOString(),
        totalErrors: errors.length,
        totalAlerts: alerts.length,
        errors,
        alerts
      };

      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=rum-data-export.json');
      res.json(exportData);

      logger.info('Data export completed', {
        errorCount: errors.length,
        alertCount: alerts.length,
        ip: req.ip
      });
    } catch (error) {
      logger.error('Failed to export data', { error: error.message, ip: req.ip });
      res.status(500).json({
        success: false,
        message: 'Failed to export data: ' + error.message
      });
    }
  })
);

/**
 * GET /admin/logs
 * Get recent system logs
 */
router.get('/logs',
  adminRateLimit,
  asyncHandler(async (req, res) => {
    const fs = require('fs');
    const config = require('../config');

    try {
      if (fs.existsSync(config.logging.file)) {
        const logs = fs.readFileSync(config.logging.file, 'utf8');
        const recentLogs = logs.split('\n').slice(-50).join('\n');
        res.type('text/plain').send(recentLogs);
      } else {
        res.type('text/plain').send('No log file found. Logs are being written to console only.');
      }
    } catch (error) {
      res.type('text/plain').send('Failed to read log file: ' + error.message);
    }
  })
);

/**
 * POST /admin/restart
 * Restart the server (graceful shutdown)
 */
router.post('/restart',
  adminRateLimit,
  asyncHandler(async (req, res) => {
    logger.info('Server restart requested from admin dashboard', { ip: req.ip });

    res.json({
      success: true,
      message: 'Server restart initiated'
    });

    // Graceful shutdown after sending response
    setTimeout(() => {
      process.kill(process.pid, 'SIGTERM');
    }, 1000);
  })
);

module.exports = router;
