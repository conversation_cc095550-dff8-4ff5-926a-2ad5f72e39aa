/**
 * Error logging routes
 */

const express = require('express');
const router = express.Router();

const ErrorLog = require('../models/ErrorLog');
const { InMemoryErrorLog } = require('../models/InMemoryStorage');
const { validateErrorLog, validateQueryParams, validateDateRange } = require('../middleware/validation');
const { errorLogRateLimit } = require('../middleware/rateLimiter');
const { asyncHandler } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const alertService = require('../services/alertService');

// Helper function to get the appropriate model
function getErrorModel() {
  return global.useInMemoryStorage ? InMemoryErrorLog : ErrorLog;
}

/**
 * POST /api/errors
 * Log error batch
 */
router.post('/',
  errorLogRateLimit,
  validateErrorLog,
  asyncHandler(async (req, res) => {
    const { errors, sessionId, batchId } = req.body;

    logger.info('Received error batch', {
      batchId,
      sessionId,
      errorCount: errors.length,
      ip: req.ip
    });

    // Process each error in the batch
    const savedErrors = [];
    const criticalErrors = [];

    for (const errorData of errors) {
      try {
        // Create error log entry
        const ErrorModel = getErrorModel();
        let savedError;

        if (global.useInMemoryStorage) {
          const inMemoryLog = new InMemoryErrorLog();
          savedError = await inMemoryLog.save({
            ...errorData,
            batchId,
            createdAt: new Date()
          });
        } else {
          const errorLog = new ErrorLog({
            ...errorData,
            batchId,
            createdAt: new Date()
          });
          savedError = await errorLog.save();
        }

        savedErrors.push(savedError);

        // Track critical errors for alerting
        if (errorData.severity === 'critical') {
          criticalErrors.push(savedError);
        }

        logger.debug('Error logged', {
          errorId: errorData.errorId,
          type: errorData.type,
          severity: errorData.severity
        });

      } catch (saveError) {
        logger.error('Failed to save error log', {
          errorId: errorData.errorId,
          error: saveError.message,
          stack: saveError.stack
        });
      }
    }

    // Check if we need to send alerts
    if (savedErrors.length > 0) {
      try {
        await alertService.checkAndSendAlerts(sessionId, savedErrors);
      } catch (alertError) {
        logger.error('Failed to process alerts', {
          sessionId,
          error: alertError.message
        });
      }
    }

    res.status(201).json({
      success: true,
      message: 'Errors logged successfully',
      data: {
        batchId,
        processedCount: savedErrors.length,
        totalCount: errors.length,
        criticalCount: criticalErrors.length
      }
    });
  })
);

/**
 * GET /api/errors
 * Get error logs with filtering and pagination
 */
router.get('/',
  validateQueryParams,
  validateDateRange,
  asyncHandler(async (req, res) => {
    const {
      page,
      limit,
      sortBy,
      sortOrder,
      type,
      severity,
      environment,
      sessionId,
      startDate,
      endDate,
      timeRange
    } = req.query;

    // Build query
    const query = {};

    if (type) query.type = type;
    if (severity) query.severity = severity;
    if (environment) query.environment = environment;
    if (sessionId) query.sessionId = sessionId;

    // Date filtering
    if (startDate && endDate) {
      query.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else if (timeRange) {
      query.createdAt = {
        $gte: new Date(Date.now() - timeRange * 60 * 60 * 1000)
      };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;
    const sort = { [sortBy]: sortOrder === 'asc' ? 1 : -1 };

    // Execute query
    const ErrorModel = getErrorModel();
    const [errors, totalCount] = await Promise.all([
      ErrorModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      ErrorModel.countDocuments(query)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.json({
      success: true,
      data: {
        errors,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
          hasNextPage,
          hasPrevPage
        }
      }
    });
  })
);

/**
 * GET /api/errors/:errorId
 * Get specific error by ID
 */
router.get('/:errorId',
  asyncHandler(async (req, res) => {
    const { errorId } = req.params;
    const ErrorModel = getErrorModel();

    const error = await ErrorModel.findOne({ errorId }).lean();

    if (!error) {
      return res.status(404).json({
        success: false,
        error: 'Error not found',
        code: 'ERROR_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      data: { error }
    });
  })
);

/**
 * GET /api/errors/session/:sessionId
 * Get all errors for a specific session
 */
router.get('/session/:sessionId',
  validateQueryParams,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const { page, limit, sortBy, sortOrder } = req.query;
    const ErrorModel = getErrorModel();

    const skip = (page - 1) * limit;
    const sort = { [sortBy]: sortOrder === 'asc' ? 1 : -1 };

    const [errors, totalCount] = await Promise.all([
      ErrorModel.find({ sessionId })
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      ErrorModel.countDocuments({ sessionId })
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    res.json({
      success: true,
      data: {
        sessionId,
        errors,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        }
      }
    });
  })
);

/**
 * DELETE /api/errors/session/:sessionId
 * Delete all errors for a specific session (admin only)
 */
router.delete('/session/:sessionId',
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params;
    const ErrorModel = getErrorModel();

    const result = await ErrorModel.deleteMany({ sessionId });

    logger.info('Deleted errors for session', {
      sessionId,
      deletedCount: result.deletedCount,
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Session errors deleted successfully',
      data: {
        sessionId,
        deletedCount: result.deletedCount
      }
    });
  })
);

module.exports = router;
