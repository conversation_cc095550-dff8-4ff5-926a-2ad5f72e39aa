/**
 * Statistics and analytics routes
 */

const express = require('express');
const router = express.Router();

const ErrorLog = require('../models/ErrorLog');
const AlertLog = require('../models/AlertLog');
const { InMemoryErrorLog, InMemoryAlertLog } = require('../models/InMemoryStorage');

// Helper functions to get the appropriate models
function getErrorModel() {
  return global.useInMemoryStorage ? InMemoryErrorLog : ErrorLog;
}

function getAlertModel() {
  return global.useInMemoryStorage ? InMemoryAlertLog : AlertLog;
}
const { validateStatsQuery } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

/**
 * GET /api/stats/overview
 * Get general error statistics overview
 */
router.get('/overview',
  validateStatsQuery,
  asyncHandler(async (req, res) => {
    const { timeRange } = req.query;
    const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);

    // Get basic counts
    const ErrorModel = getErrorModel();
    const [
      totalErrors,
      criticalErrors,
      uniqueSessions,
      errorsByType,
      errorsBySeverity,
      recentErrors
    ] = await Promise.all([
      ErrorModel.countDocuments({ createdAt: { $gte: startTime } }),
      ErrorModel.countDocuments({
        createdAt: { $gte: startTime },
        severity: 'critical'
      }),
      ErrorModel.distinct('sessionId', { createdAt: { $gte: startTime } }),
      ErrorModel.aggregate([
        { $match: { createdAt: { $gte: startTime } } },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      ErrorModel.aggregate([
        { $match: { createdAt: { $gte: startTime } } },
        { $group: { _id: '$severity', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      ErrorModel.find({ createdAt: { $gte: startTime } })
        .sort({ createdAt: -1 })
        .limit(10)
        .select('errorId type severity message createdAt sessionId')
        .lean()
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalErrors,
          criticalErrors,
          uniqueSessions: uniqueSessions.length,
          timeRange: `${timeRange} hours`
        },
        breakdown: {
          byType: errorsByType,
          bySeverity: errorsBySeverity
        },
        recentErrors
      }
    });
  })
);

/**
 * GET /api/stats/trends
 * Get error trends over time
 */
router.get('/trends',
  validateStatsQuery,
  asyncHandler(async (req, res) => {
    const { timeRange, interval } = req.query;

    const trends = await ErrorLog.getErrorTrends(timeRange, interval);

    res.json({
      success: true,
      data: {
        trends,
        timeRange: `${timeRange} hours`,
        interval: `${interval} hour(s)`
      }
    });
  })
);

/**
 * GET /api/stats/top-errors
 * Get most frequent errors
 */
router.get('/top-errors',
  validateStatsQuery,
  asyncHandler(async (req, res) => {
    const { timeRange } = req.query;
    const limit = parseInt(req.query.limit) || 10;

    const topErrors = await ErrorLog.getTopErrors(limit, timeRange);

    res.json({
      success: true,
      data: {
        topErrors,
        timeRange: `${timeRange} hours`,
        limit
      }
    });
  })
);

/**
 * GET /api/stats/sessions
 * Get session statistics
 */
router.get('/sessions',
  validateStatsQuery,
  asyncHandler(async (req, res) => {
    const { timeRange } = req.query;
    const startTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);

    const sessionStats = await ErrorLog.aggregate([
      { $match: { createdAt: { $gte: startTime } } },
      {
        $group: {
          _id: '$sessionId',
          errorCount: { $sum: 1 },
          criticalCount: {
            $sum: { $cond: [{ $eq: ['$severity', 'critical'] }, 1, 0] }
          },
          firstError: { $min: '$createdAt' },
          lastError: { $max: '$createdAt' },
          errorTypes: { $addToSet: '$type' },
          userAgent: { $first: '$userAgent' }
        }
      },
      {
        $addFields: {
          sessionDuration: {
            $subtract: ['$lastError', '$firstError']
          },
          typeCount: { $size: '$errorTypes' }
        }
      },
      { $sort: { errorCount: -1 } },
      { $limit: 50 }
    ]);

    res.json({
      success: true,
      data: {
        sessions: sessionStats,
        timeRange: `${timeRange} hours`
      }
    });
  })
);

/**
 * GET /api/stats/alerts
 * Get alert statistics
 */
router.get('/alerts',
  validateStatsQuery,
  asyncHandler(async (req, res) => {
    const { timeRange } = req.query;

    const [alertStats, recentAlerts] = await Promise.all([
      AlertLog.getAlertStats(timeRange),
      AlertLog.find({
        createdAt: { $gte: new Date(Date.now() - timeRange * 60 * 60 * 1000) }
      })
        .sort({ createdAt: -1 })
        .limit(20)
        .lean()
    ]);

    res.json({
      success: true,
      data: {
        alertStats,
        recentAlerts,
        timeRange: `${timeRange} hours`
      }
    });
  })
);

/**
 * GET /api/stats/health
 * Get system health metrics
 */
router.get('/health',
  asyncHandler(async (req, res) => {
    const now = new Date();
    const oneHourAgo = new Date(now - 60 * 60 * 1000);
    const oneDayAgo = new Date(now - 24 * 60 * 60 * 1000);

    const [
      errorsLastHour,
      errorsLastDay,
      criticalErrorsLastHour,
      activeSessionsLastHour,
      dbStats
    ] = await Promise.all([
      ErrorLog.countDocuments({ createdAt: { $gte: oneHourAgo } }),
      ErrorLog.countDocuments({ createdAt: { $gte: oneDayAgo } }),
      ErrorLog.countDocuments({
        createdAt: { $gte: oneHourAgo },
        severity: 'critical'
      }),
      ErrorLog.distinct('sessionId', { createdAt: { $gte: oneHourAgo } }),
      ErrorLog.collection.stats()
    ]);

    // Calculate health score (0-100)
    let healthScore = 100;

    // Deduct points for high error rates
    if (errorsLastHour > 100) healthScore -= 20;
    else if (errorsLastHour > 50) healthScore -= 10;

    // Deduct points for critical errors
    if (criticalErrorsLastHour > 10) healthScore -= 30;
    else if (criticalErrorsLastHour > 5) healthScore -= 15;
    else if (criticalErrorsLastHour > 0) healthScore -= 5;

    // Determine status
    let status = 'healthy';
    if (healthScore < 50) status = 'critical';
    else if (healthScore < 70) status = 'warning';
    else if (healthScore < 90) status = 'degraded';

    res.json({
      success: true,
      data: {
        status,
        healthScore,
        metrics: {
          errorsLastHour,
          errorsLastDay,
          criticalErrorsLastHour,
          activeSessionsLastHour: activeSessionsLastHour.length,
          database: {
            totalDocuments: dbStats.count,
            storageSize: dbStats.storageSize,
            indexSize: dbStats.totalIndexSize
          }
        },
        timestamp: now.toISOString()
      }
    });
  })
);

module.exports = router;
