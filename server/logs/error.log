{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 12:58:58"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 13:00:00"}
{"body":{},"ip":"::1","level":"error","message":"Server error Not Found - /favicon.ico","method":"GET","params":{},"query":{},"service":"rum-server","timestamp":"2025-07-07 13:11:15","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"initializeDemoData is not defined","level":"error","message":"Failed to start server","service":"rum-server","stack":"ReferenceError: initializeDemoData is not defined\n    at connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:117:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:127:5)","timestamp":"2025-07-07 14:19:12"}
{"body":{},"ip":"::ffff:127.0.0.1","level":"error","message":"Server error ErrorModel.find(...).sort is not a function","method":"GET","params":{},"query":{"groupBy":"severity","interval":1,"timeRange":24},"service":"rum-server","timestamp":"2025-07-07 14:20:58","url":"/api/stats/overview","userAgent":"curl/8.1.2"}
{"error":"Cannot redefine property: isLocked","ip":"::ffff:127.0.0.1","level":"error","message":"Admin login error","service":"rum-server","timestamp":"2025-07-07 14:23:47"}
{"error":"Cannot redefine property: isLocked","ip":"::ffff:127.0.0.1","level":"error","message":"Admin login error","service":"rum-server","timestamp":"2025-07-07 14:24:57"}
