{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 12:58:58"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 13:00:00"}
