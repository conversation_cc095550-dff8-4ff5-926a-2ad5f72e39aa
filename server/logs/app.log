{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 12:58:53"}
{"level":"warn","message":"SMS service not configured - missing <PERSON><PERSON><PERSON> credentials","service":"rum-server","timestamp":"2025-07-07 12:58:53"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 12:58:58"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 12:59:55"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 12:59:55"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 13:00:00"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:09"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:09"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:14"}
{"environment":"development","level":"info","message":"Server started","pid":86623,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:14"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:24"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:24"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:29"}
{"environment":"development","level":"info","message":"Server started","pid":86940,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:29"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:35"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:35"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:40"}
{"environment":"development","level":"info","message":"Server started","pid":87162,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:40"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:51"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:51"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:56"}
{"environment":"development","level":"info","message":"Server started","pid":87488,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:56"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:05"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:05"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:10"}
{"environment":"development","level":"info","message":"Server started","pid":87757,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:10"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:18"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:18"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:23"}
{"environment":"development","level":"info","message":"Server started","pid":88012,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:23"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:33"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:33"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:38"}
{"environment":"development","level":"info","message":"Server started","pid":88294,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:38"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:46"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:46"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:51"}
{"environment":"development","level":"info","message":"Server started","pid":88544,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:51"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:02:00"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:02:00"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:02:05"}
{"environment":"development","level":"info","message":"Server started","pid":88829,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:02:05"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:02:22"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:02:22"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:02:27"}
{"environment":"development","level":"info","message":"Server started","pid":89255,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:02:27"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:02:32"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:02:32"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:02:37"}
{"environment":"development","level":"info","message":"Server started","pid":89463,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:02:37"}
{"level":"info","message":"::ffff:127.0.0.1 - - [07/Jul/2025:07:33:28 +0000] \"GET /health HTTP/1.1\" 200 100 \"-\" \"curl/8.1.2\"","service":"rum-server","timestamp":"2025-07-07 13:03:28"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:15"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:15"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:10:20"}
{"environment":"development","level":"info","message":"Server started","pid":96922,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:10:20"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:28"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:28"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:10:33"}
{"environment":"development","level":"info","message":"Server started","pid":97204,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:10:33"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:41"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:41"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:10:46"}
{"environment":"development","level":"info","message":"Server started","pid":97454,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:10:46"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:57"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:57"}
