{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 12:58:53"}
{"level":"warn","message":"SMS service not configured - missing <PERSON><PERSON><PERSON> credentials","service":"rum-server","timestamp":"2025-07-07 12:58:53"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 12:58:58"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 12:59:55"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 12:59:55"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"error","message":"MongoDB connection error","service":"rum-server","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1165:11)\n    at NativeConnection.openUri (/Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/mongoose/lib/connection.js:1096:11)\n    at async connectDatabase (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:88:5)\n    at async startServer (/Users/<USER>/projects/vijayPoc/rumAlerting/server/src/index.js:108:5)","timestamp":"2025-07-07 13:00:00"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:09"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:09"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:14"}
{"environment":"development","level":"info","message":"Server started","pid":86623,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:14"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:24"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:24"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:29"}
{"environment":"development","level":"info","message":"Server started","pid":86940,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:29"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:35"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:35"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:40"}
{"environment":"development","level":"info","message":"Server started","pid":87162,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:40"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:00:51"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:00:51"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:00:56"}
{"environment":"development","level":"info","message":"Server started","pid":87488,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:00:56"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:05"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:05"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:10"}
{"environment":"development","level":"info","message":"Server started","pid":87757,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:10"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:18"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:18"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:23"}
{"environment":"development","level":"info","message":"Server started","pid":88012,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:23"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:33"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:33"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:38"}
{"environment":"development","level":"info","message":"Server started","pid":88294,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:38"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:01:46"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:01:46"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:01:51"}
{"environment":"development","level":"info","message":"Server started","pid":88544,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:01:51"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:02:00"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:02:00"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:02:05"}
{"environment":"development","level":"info","message":"Server started","pid":88829,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:02:05"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:02:22"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:02:22"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:02:27"}
{"environment":"development","level":"info","message":"Server started","pid":89255,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:02:27"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:02:32"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:02:32"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:02:37"}
{"environment":"development","level":"info","message":"Server started","pid":89463,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:02:37"}
{"level":"info","message":"::ffff:127.0.0.1 - - [07/Jul/2025:07:33:28 +0000] \"GET /health HTTP/1.1\" 200 100 \"-\" \"curl/8.1.2\"","service":"rum-server","timestamp":"2025-07-07 13:03:28"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:15"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:15"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:10:20"}
{"environment":"development","level":"info","message":"Server started","pid":96922,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:10:20"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:28"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:28"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:10:33"}
{"environment":"development","level":"info","message":"Server started","pid":97204,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:10:33"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:41"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:41"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:10:46"}
{"environment":"development","level":"info","message":"Server started","pid":97454,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:10:46"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:10:57"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:10:57"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:11:02"}
{"environment":"development","level":"info","message":"Server started","pid":97770,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:11:02"}
{"level":"info","message":"::1 - - [07/Jul/2025:07:41:15 +0000] \"GET /admin HTTP/1.1\" 200 - \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"rum-server","timestamp":"2025-07-07 13:11:15"}
{"body":{},"ip":"::1","level":"error","message":"Server error Not Found - /favicon.ico","method":"GET","params":{},"query":{},"service":"rum-server","timestamp":"2025-07-07 13:11:15","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"::1 - - [07/Jul/2025:07:41:15 +0000] \"GET /favicon.ico HTTP/1.1\" 404 60 \"-\" \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"rum-server","timestamp":"2025-07-07 13:11:15"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:14:20"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:14:20"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:14:25"}
{"environment":"development","level":"info","message":"Server started","pid":1612,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:14:25"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:14:49"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:14:49"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:14:54"}
{"environment":"development","level":"info","message":"Server started","pid":2160,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:14:54"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:14:58"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:14:58"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:15:03"}
{"environment":"development","level":"info","message":"Server started","pid":2352,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:15:03"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:15:13"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:15:13"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:15:18"}
{"environment":"development","level":"info","message":"Server started","pid":2685,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:15:18"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:15:53"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:15:53"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:15:58"}
{"environment":"development","level":"info","message":"Server started","pid":3360,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:15:58"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:16:06"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:16:06"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:16:11"}
{"environment":"development","level":"info","message":"Server started","pid":3640,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:16:11"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:23:49"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:23:49"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:23:55"}
{"environment":"development","level":"info","message":"Server started","pid":12162,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:23:55"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:34:48"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:34:48"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:34:54"}
{"environment":"development","level":"info","message":"Server started","pid":23636,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:34:54"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:35:11"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:35:11"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:35:16"}
{"environment":"development","level":"info","message":"Server started","pid":24083,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:35:16"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:36:23"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:36:23"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:36:28"}
{"environment":"development","level":"info","message":"Server started","pid":25347,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:36:29"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:36:51"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:36:51"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:36:56"}
{"environment":"development","level":"info","message":"Server started","pid":26077,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:36:56"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:37:15"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:37:15"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:37:20"}
{"environment":"development","level":"info","message":"Server started","pid":26548,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:37:20"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:37:28"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:37:28"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:37:33"}
{"environment":"development","level":"info","message":"Server started","pid":26802,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:37:33"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:37:41"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:37:41"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:37:46"}
{"environment":"development","level":"info","message":"Server started","pid":27078,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:37:46"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:37:56"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:37:56"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:38:01"}
{"environment":"development","level":"info","message":"Server started","pid":27368,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:38:01"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:38:11"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:38:11"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:38:16"}
{"environment":"development","level":"info","message":"Server started","pid":27648,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:38:16"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:38:26"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:38:26"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:38:31"}
{"environment":"development","level":"info","message":"Server started","pid":27933,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:38:31"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:38:45"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:38:45"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:38:50"}
{"environment":"development","level":"info","message":"Server started","pid":28318,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:38:50"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:39:08"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:39:08"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:39:13"}
{"environment":"development","level":"info","message":"Server started","pid":28745,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:39:13"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:39:26"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:39:26"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:39:31"}
{"environment":"development","level":"info","message":"Server started","pid":29098,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:39:31"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:39:39"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:39:39"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:39:44"}
{"environment":"development","level":"info","message":"Server started","pid":29378,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:39:44"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:39:52"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:39:52"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:39:57"}
{"environment":"development","level":"info","message":"Server started","pid":29623,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:39:57"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:40:11"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:40:11"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:40:16"}
{"environment":"development","level":"info","message":"Server started","pid":30016,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:40:16"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:40:27"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:40:27"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:40:33"}
{"environment":"development","level":"info","message":"Server started","pid":30296,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:40:33"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:40:49"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:40:49"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:40:54"}
{"environment":"development","level":"info","message":"Server started","pid":30715,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:40:54"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:41:02"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:41:02"}
{"error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","level":"warn","message":"MongoDB connection failed, using in-memory storage for demo","service":"rum-server","timestamp":"2025-07-07 13:41:07"}
{"environment":"development","level":"info","message":"Server started","pid":30962,"port":"3001","service":"rum-server","timestamp":"2025-07-07 13:41:07"}
{"level":"warn","message":"Email service not configured - missing SMTP credentials","service":"rum-server","timestamp":"2025-07-07 13:41:24"}
{"level":"warn","message":"SMS service not configured - missing Twilio credentials","service":"rum-server","timestamp":"2025-07-07 13:41:24"}
