/**
 * <PERSON><PERSON>t to create initial admin user
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../src/models/User');
const Project = require('../src/models/Project');
const { InMemoryUser, InMemoryProject } = require('../src/models/InMemoryStorage');
const config = require('../src/config');

async function createAdminUser() {
  try {
    // Use in-memory storage for demo
    global.useInMemoryStorage = true;

    // Initialize in-memory storage
    if (!global.inMemoryStorage) {
      global.inMemoryStorage = {
        errors: [],
        alertLogs: [],
        users: [],
        projects: []
      };
    }

    console.log('Using in-memory storage for demo');

    // Check if admin user already exists
    const existingAdmin = await InMemoryUser.findByEmail('<EMAIL>');
    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }

    // Create admin user
    const adminUser = await InMemoryUser.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      name: 'RUM Admin',
      role: 'super_admin',
      organization: 'RUM Demo Organization',
      department: 'IT',
      status: 'active'
    });

    console.log('Admin user created successfully:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('Role: super_admin');
    console.log('User ID:', adminUser.userId);

    // Create a demo project
    const inMemoryProject = new InMemoryProject();
    const demoProject = await inMemoryProject.save({
      projectId: 'demo_project_001',
      name: 'Demo Website',
      description: 'Demo project for testing RUM system',
      domain: 'localhost:3000',
      allowedOrigins: ['http://localhost:3000', 'http://localhost:3002'],
      apiKey: 'demo_api_key_12345',
      status: 'active',
      owner: {
        userId: adminUser.userId,
        name: adminUser.name,
        email: adminUser.email
      },
      alertConfig: {
        enabled: true,
        errorThreshold: 3,
        timeWindowMinutes: 2,
        emailRecipients: [
          {
            email: '<EMAIL>',
            name: 'RUM Admin',
            role: 'admin'
          }
        ],
        smsRecipients: [],
        emailCooldownMinutes: 30,
        smsCooldownMinutes: 60
      },
      rumConfig: {
        enableLogging: true,
        enableConsoleCapture: true,
        enableNetworkCapture: true,
        enableResourceCapture: true,
        maxErrorsPerSession: 100,
        batchSize: 10,
        batchTimeout: 5000
      },
      stats: {
        totalErrors: 0
      }
    });

    console.log('\nDemo project created:');
    console.log('Project ID: demo_project_001');
    console.log('API Key: demo_api_key_12345');
    console.log('Domain: localhost:3000');

    console.log('\n✅ Setup completed successfully!');
    console.log('\nYou can now:');
    console.log('1. Login to admin panel at http://localhost:3001/admin');
    console.log('2. Use the demo project API key in your RUM script');
    console.log('3. Configure additional projects and users');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    process.exit(0);
  }
}

// Run the script
createAdminUser();
