{"name": "@rum-alerting/server", "version": "1.0.0", "description": "RUM Error Logging Server API", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "lint": "eslint src/**/*.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "mongoose": "^8.0.3", "dotenv": "^16.3.1", "joi": "^17.11.0", "rate-limiter-flexible": "^4.0.1", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "winston": "^3.11.0", "compression": "^1.7.4", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.57.0"}, "keywords": ["api", "error-logging", "monitoring", "alerting"], "author": "Your Name", "license": "MIT"}