{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/vijayPoc/rumAlerting/demo-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ndeclare global {\n  interface Window {\n    RUM?: {\n      logError: (message: string, details?: any) => void;\n      getStats: () => any;\n      updateConfig: (config: any) => void;\n      clearErrors: () => void;\n    };\n  }\n}\n\nexport default function Home() {\n  const [rumStats, setRumStats] = useState<any>(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Update RUM stats periodically\n  useEffect(() => {\n    const updateStats = () => {\n      if (window.RUM) {\n        setRumStats(window.RUM.getStats());\n      }\n    };\n\n    updateStats();\n    const interval = setInterval(updateStats, 5000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const triggerJavaScriptError = () => {\n    setIsLoading(true);\n    setTimeout(() => {\n      // This will trigger a JavaScript error\n      (window as any).nonExistentFunction();\n      setIsLoading(false);\n    }, 1000);\n  };\n\n  const triggerNetworkError = async () => {\n    setIsLoading(true);\n    try {\n      // This will trigger a network error\n      await fetch('/api/non-existent-endpoint');\n    } catch (error) {\n      console.log('Network error triggered');\n    }\n    setIsLoading(false);\n  };\n\n  const triggerConsoleError = () => {\n    console.error('This is a test console error from the demo app');\n  };\n\n  const triggerCustomError = () => {\n    if (window.RUM) {\n      window.RUM.logError('Custom error triggered by user', {\n        severity: 'high',\n        component: 'demo-page',\n        action: 'button-click',\n        metadata: {\n          timestamp: new Date().toISOString(),\n          userAction: 'trigger-custom-error'\n        }\n      });\n    }\n  };\n\n  const clearAllErrors = () => {\n    if (window.RUM) {\n      window.RUM.clearErrors();\n      setRumStats(window.RUM.getStats());\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8\">\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Header */}\n        <header className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            🚨 RUM Alerting System Demo\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Real User Monitoring with Error Logging and Alerting.\n            This demo showcases error capture, logging, and user recovery features.\n          </p>\n        </header>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Error Testing Panel */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-800 mb-6 flex items-center\">\n              🧪 Error Testing Panel\n            </h2>\n\n            <div className=\"space-y-4\">\n              <button\n                onClick={triggerJavaScriptError}\n                disabled={isLoading}\n                className=\"w-full bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n              >\n                {isLoading ? 'Triggering...' : 'Trigger JavaScript Error'}\n              </button>\n\n              <button\n                onClick={triggerNetworkError}\n                disabled={isLoading}\n                className=\"w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-300 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n              >\n                {isLoading ? 'Triggering...' : 'Trigger Network Error'}\n              </button>\n\n              <button\n                onClick={triggerConsoleError}\n                className=\"w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n              >\n                Trigger Console Error\n              </button>\n\n              <button\n                onClick={triggerCustomError}\n                className=\"w-full bg-purple-500 hover:bg-purple-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n              >\n                Trigger Custom Error\n              </button>\n\n              <button\n                onClick={clearAllErrors}\n                className=\"w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n              >\n                Clear All Errors\n              </button>\n            </div>\n\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-semibold text-blue-800 mb-2\">💡 What happens when you trigger errors:</h3>\n              <ul className=\"text-sm text-blue-700 space-y-1\">\n                <li>• Errors are captured automatically by the RUM script</li>\n                <li>• Error details are sent to the server asynchronously</li>\n                <li>• User alerts appear for critical/high severity errors</li>\n                <li>• Admin notifications are sent when thresholds are exceeded</li>\n              </ul>\n            </div>\n          </div>\n\n          {/* RUM Statistics Panel */}\n          <div className=\"bg-white rounded-lg shadow-lg p-6\">\n            <h2 className=\"text-2xl font-semibold text-gray-800 mb-6 flex items-center\">\n              📊 RUM Statistics\n            </h2>\n\n            {rumStats ? (\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"bg-blue-50 p-4 rounded-lg\">\n                    <div className=\"text-2xl font-bold text-blue-600\">\n                      {rumStats.logger?.totalErrors || 0}\n                    </div>\n                    <div className=\"text-sm text-blue-800\">Total Errors</div>\n                  </div>\n\n                  <div className=\"bg-green-50 p-4 rounded-lg\">\n                    <div className=\"text-2xl font-bold text-green-600\">\n                      {rumStats.logger?.queuedErrors || 0}\n                    </div>\n                    <div className=\"text-sm text-green-800\">Queued Errors</div>\n                  </div>\n                </div>\n\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-gray-800 mb-2\">Session Info</h3>\n                  <div className=\"text-sm text-gray-600 space-y-1\">\n                    <div><strong>Session ID:</strong> {rumStats.sessionId}</div>\n                    <div><strong>Environment:</strong> {rumStats.config?.environment}</div>\n                    <div><strong>Logging Enabled:</strong> {rumStats.config?.enableLogging ? 'Yes' : 'No'}</div>\n                  </div>\n                </div>\n\n                {Object.keys(rumStats.errorFrequency || {}).length > 0 && (\n                  <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                    <h3 className=\"font-semibold text-yellow-800 mb-2\">Error Frequency</h3>\n                    <div className=\"text-sm text-yellow-700\">\n                      {Object.entries(rumStats.errorFrequency).map(([time, count]) => (\n                        <div key={time}>\n                          {new Date(parseInt(time)).toLocaleTimeString()}: {count as number} errors\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"text-center text-gray-500 py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4\"></div>\n                Loading RUM statistics...\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Features Overview */}\n        <div className=\"mt-12 bg-white rounded-lg shadow-lg p-8\">\n          <h2 className=\"text-2xl font-semibold text-gray-800 mb-6 text-center\">\n            🚀 RUM System Features\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">🔍</div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Error Capture</h3>\n              <p className=\"text-sm text-gray-600\">\n                Automatically captures JavaScript, network, console, and resource errors\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">📡</div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Async Logging</h3>\n              <p className=\"text-sm text-gray-600\">\n                Sends error data to server without blocking the main thread\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">🚨</div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Smart Alerts</h3>\n              <p className=\"text-sm text-gray-600\">\n                Triggers email/SMS alerts when error thresholds are exceeded\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">🔧</div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">User Recovery</h3>\n              <p className=\"text-sm text-gray-600\">\n                Provides retry, reload, and support contact options for users\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <footer className=\"mt-12 text-center text-gray-600\">\n          <p>\n            RUM Alerting System Demo - Built with Next.js, Express.js, and MongoDB\n          </p>\n          <p className=\"text-sm mt-2\">\n            Check the browser console and network tab to see the RUM script in action!\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAee,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;8CAAc;oBAClB,IAAI,OAAO,GAAG,EAAE;wBACd,YAAY,OAAO,GAAG,CAAC,QAAQ;oBACjC;gBACF;;YAEA;YACA,MAAM,WAAW,YAAY,aAAa;YAC1C;kCAAO,IAAM,cAAc;;QAC7B;yBAAG,EAAE;IAEL,MAAM,yBAAyB;QAC7B,aAAa;QACb,WAAW;YACT,uCAAuC;YACtC,OAAe,mBAAmB;YACnC,aAAa;QACf,GAAG;IACL;IAEA,MAAM,sBAAsB;QAC1B,aAAa;QACb,IAAI;YACF,oCAAoC;YACpC,MAAM,MAAM;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;QACA,aAAa;IACf;IAEA,MAAM,sBAAsB;QAC1B,QAAQ,KAAK,CAAC;IAChB;IAEA,MAAM,qBAAqB;QACzB,IAAI,OAAO,GAAG,EAAE;YACd,OAAO,GAAG,CAAC,QAAQ,CAAC,kCAAkC;gBACpD,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;oBACR,WAAW,IAAI,OAAO,WAAW;oBACjC,YAAY;gBACd;YACF;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,OAAO,GAAG,EAAE;YACd,OAAO,GAAG,CAAC,WAAW;YACtB,YAAY,OAAO,GAAG,CAAC,QAAQ;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAI5E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,YAAY,kBAAkB;;;;;;sDAGjC,6LAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,YAAY,kBAAkB;;;;;;sDAGjC,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAID,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAID,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;8DACJ,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAMV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;gCAI3E,yBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,SAAS,MAAM,EAAE,eAAe;;;;;;sEAEnC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAGzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,SAAS,MAAM,EAAE,gBAAgB;;;;;;sEAEpC,6LAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;sDAI5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EAAI,6LAAC;8EAAO;;;;;;gEAAoB;gEAAE,SAAS,SAAS;;;;;;;sEACrD,6LAAC;;8EAAI,6LAAC;8EAAO;;;;;;gEAAqB;gEAAE,SAAS,MAAM,EAAE;;;;;;;sEACrD,6LAAC;;8EAAI,6LAAC;8EAAO;;;;;;gEAAyB;gEAAE,SAAS,MAAM,EAAE,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;;wCAIpF,OAAO,IAAI,CAAC,SAAS,cAAc,IAAI,CAAC,GAAG,MAAM,GAAG,mBACnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,6LAAC;oDAAI,WAAU;8DACZ,OAAO,OAAO,CAAC,SAAS,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,iBACzD,6LAAC;;gEACE,IAAI,KAAK,SAAS,OAAO,kBAAkB;gEAAG;gEAAG;gEAAgB;;2DAD1D;;;;;;;;;;;;;;;;;;;;;yDASpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;wCAAkF;;;;;;;;;;;;;;;;;;;8BAQzG,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAItE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;8BAQ3C,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;sCAAE;;;;;;sCAGH,6LAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GAjPwB;KAAA", "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/projects/vijayPoc/rumAlerting/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}