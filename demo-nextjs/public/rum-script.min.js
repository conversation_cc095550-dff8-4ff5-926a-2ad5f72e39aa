!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).RUMScript={})}(this,function(e){"use strict";const t={logEndpoint:"/api/errors",baseUrl:"http://localhost:3001",errorThreshold:5,timeWindow:3e5,environment:"production",enableLogging:!0,enableConsoleCapture:!0,enableNetworkCapture:!0,enableResourceCapture:!0,maxErrorsPerSession:100,batchSize:10,batchTimeout:5e3,showUserAlerts:!0,alertPosition:"top-right",alertDuration:5e3,apiKey:null,sanitizeData:!0,excludeUrls:[],excludeMessages:[],maxRetries:3,retryDelay:1e3,debug:!1,verbose:!1},r="low",n="medium",o="high",s="critical",i="javascript",a="network",l="resource",c="console",u="unhandled_rejection",h="custom",d="error",g="warning",p="info",m="success";function f(){return(new Date).toISOString()}function y(e){const t="low",r="critical";return e.message&&(e.message.includes("ChunkLoadError")||e.message.includes("Loading chunk")||e.message.includes("Cannot read property")||e.message.includes("is not a function"))?r:t}function w(e,t){const r=Object.assign({},e);return b(e)&&b(t)&&Object.keys(t).forEach(n=>{b(t[n])?n in e?r[n]=w(e[n],t[n]):Object.assign(r,{[n]:t[n]}):Object.assign(r,{[n]:t[n]})}),r}function b(e){return e&&"object"==typeof e&&!Array.isArray(e)}class E{constructor(e,t){this.config=e,this.logger=t,this.originalConsoleError=console.error,this.originalConsoleWarn=console.warn,this.originalFetch=window.fetch,this.originalXHROpen=XMLHttpRequest.prototype.open,this.originalXHRSend=XMLHttpRequest.prototype.send,this.setupErrorHandlers()}setupErrorHandlers(){this.setupJavaScriptErrorHandler(),this.setupUnhandledRejectionHandler(),this.setupResourceErrorHandler(),this.config.enableConsoleCapture&&this.setupConsoleErrorHandler(),this.config.enableNetworkCapture&&this.setupNetworkErrorHandler()}setupJavaScriptErrorHandler(){window.addEventListener("error",e=>{const t={type:i,message:e.message,filename:e.filename,lineno:e.lineno,colno:e.colno,stack:e.error?e.error.stack:null,timestamp:f(),url:window.location.href,userAgent:navigator.userAgent,severity:y({message:e.message})};this.logger.logError(t)})}setupUnhandledRejectionHandler(){window.addEventListener("unhandledrejection",e=>{const t={type:u,message:e.reason?e.reason.toString():"Unhandled Promise Rejection",stack:e.reason&&e.reason.stack?e.reason.stack:null,timestamp:f(),url:window.location.href,userAgent:navigator.userAgent,severity:o};this.logger.logError(t)})}setupResourceErrorHandler(){this.config.enableResourceCapture&&window.addEventListener("error",e=>{if(e.target!==window&&e.target.tagName){const t={type:l,message:`Failed to load ${e.target.tagName.toLowerCase()}: ${e.target.src||e.target.href}`,resourceType:e.target.tagName.toLowerCase(),resourceUrl:e.target.src||e.target.href,timestamp:f(),url:window.location.href,userAgent:navigator.userAgent,severity:n};this.logger.logError(t)}},!0)}setupConsoleErrorHandler(){const e=this;console.error=function(...t){const r={type:c,message:t.map(e=>"object"==typeof e?JSON.stringify(e):String(e)).join(" "),timestamp:f(),url:window.location.href,userAgent:navigator.userAgent,severity:n};e.logger.logError(r),e.originalConsoleError.apply(console,t)},console.warn=function(...t){if(e.config.verbose){const n={type:c,message:"[WARN] "+t.map(e=>"object"==typeof e?JSON.stringify(e):String(e)).join(" "),timestamp:f(),url:window.location.href,userAgent:navigator.userAgent,severity:r};e.logger.logError(n)}e.originalConsoleWarn.apply(console,t)}}setupNetworkErrorHandler(){this.setupFetchErrorHandler(),this.setupXHRErrorHandler()}setupFetchErrorHandler(){const e=this;window.fetch=function(...t){return e.originalFetch.apply(this,t).then(r=>{if(!r.ok){const s={type:a,message:`Fetch error: ${r.status} ${r.statusText}`,url:t[0],status:r.status,statusText:r.statusText,timestamp:f(),pageUrl:window.location.href,userAgent:navigator.userAgent,severity:r.status>=500?o:n};e.logger.logError(s)}return r}).catch(r=>{const n={type:a,message:`Fetch error: ${r.message}`,url:t[0],error:r.toString(),timestamp:f(),pageUrl:window.location.href,userAgent:navigator.userAgent,severity:o};throw e.logger.logError(n),r})}}setupXHRErrorHandler(){const e=this;XMLHttpRequest.prototype.open=function(t,r,...n){return this._rumMethod=t,this._rumUrl=r,e.originalXHROpen.apply(this,[t,r,...n])},XMLHttpRequest.prototype.send=function(...t){const r=this;return r.addEventListener("error",function(){const t={type:a,message:`XHR error: ${r._rumMethod} ${r._rumUrl}`,method:r._rumMethod,url:r._rumUrl,timestamp:f(),pageUrl:window.location.href,userAgent:navigator.userAgent,severity:o};e.logger.logError(t)}),r.addEventListener("load",function(){if(r.status>=400){const t={type:a,message:`XHR error: ${r.status} ${r.statusText}`,method:r._rumMethod,url:r._rumUrl,status:r.status,statusText:r.statusText,timestamp:f(),pageUrl:window.location.href,userAgent:navigator.userAgent,severity:r.status>=500?o:n};e.logger.logError(t)}}),e.originalXHRSend.apply(this,t)}}logCustomError(e,t={}){const r={type:h,message:e,...t,timestamp:f(),url:window.location.href,userAgent:navigator.userAgent,severity:t.severity||n};this.logger.logError(r)}destroy(){console.error=this.originalConsoleError,console.warn=this.originalConsoleWarn,window.fetch=this.originalFetch,XMLHttpRequest.prototype.open=this.originalXHROpen,XMLHttpRequest.prototype.send=this.originalXHRSend}}class C{constructor(e,t){this.config=e,this.sessionId=t,this.errorQueue=[],this.errorCount=0,this.lastErrorTime=null,this.retryQueue=[],this.debouncedSend=function(e,t){let r;return function(...n){clearTimeout(r),r=setTimeout(()=>{clearTimeout(r),e(...n)},t)}}(()=>this.sendBatch(),this.config.batchTimeout),this.batchInterval=setInterval(()=>{this.errorQueue.length>0&&this.sendBatch()},this.config.batchTimeout)}logError(e){if(!this.config.enableLogging)return;if(function(e,t){if(t.excludeUrls.length>0){const e=window.location.href;if(t.excludeUrls.some(t=>"string"==typeof t?e.includes(t):t instanceof RegExp&&t.test(e)))return!0}return!!(t.excludeMessages.length>0&&e.message&&t.excludeMessages.some(t=>"string"==typeof t?e.message.includes(t):t instanceof RegExp&&t.test(e.message)))}(e,this.config))return;if(this.errorCount>=this.config.maxErrorsPerSession)return void(this.config.debug&&console.warn("RUM: Max errors per session exceeded"));const t={...function(e,t){if(!t.sanitizeData)return e;const r={...e};if(r.stack&&(r.stack=r.stack.replace(/\/Users\/<USER>\/]+/g,"/Users/<USER>"),r.stack=r.stack.replace(/\/home\/<USER>\/]+/g,"/home/<USER>")),r.url)try{const e=new URL(r.url);["token","key","password","secret","auth"].forEach(t=>{e.searchParams.has(t)&&e.searchParams.set(t,"***")}),r.url=e.toString()}catch(e){}return r}(e,this.config),sessionId:this.sessionId,errorId:this.generateErrorId(),environment:this.config.environment,viewport:{width:window.innerWidth,height:window.innerHeight},screen:{width:window.screen.width,height:window.screen.height}};this.errorQueue.push(t),this.errorCount++,this.lastErrorTime=Date.now(),this.config.debug&&console.log("RUM: Error logged",t),this.errorQueue.length>=this.config.batchSize||"critical"===e.severity?this.sendBatch():this.debouncedSend()}async sendBatch(){if(0===this.errorQueue.length)return;const e=[...this.errorQueue];this.errorQueue=[];const t={errors:e,sessionId:this.sessionId,timestamp:(new Date).toISOString(),batchId:this.generateBatchId()};try{await this.sendToServer(t),this.config.debug&&console.log("RUM: Batch sent successfully",t)}catch(e){this.config.debug&&console.error("RUM: Failed to send batch",e),this.retryQueue.push({payload:t,attempts:0,timestamp:Date.now()}),this.processRetryQueue()}}async sendToServer(e){const t=`${this.config.baseUrl}${this.config.logEndpoint}`,r={"Content-Type":"application/json"};this.config.apiKey&&(r.Authorization=`Bearer ${this.config.apiKey}`);const n=await fetch(t,{method:"POST",headers:r,body:JSON.stringify(e),mode:"cors"});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);return n.json()}async processRetryQueue(){const e=Date.now(),t=[];this.retryQueue=this.retryQueue.filter(r=>{if(r.attempts>=this.config.maxRetries)return this.config.debug&&console.warn("RUM: Max retries exceeded for batch",r.payload.batchId),!1;return!(e-r.timestamp>=this.config.retryDelay*Math.pow(2,r.attempts))||(t.push(r),!1)});for(const r of t)try{await this.sendToServer(r.payload),this.config.debug&&console.log("RUM: Retry successful for batch",r.payload.batchId)}catch(t){this.config.debug&&console.error("RUM: Retry failed for batch",r.payload.batchId,t),this.retryQueue.push({...r,attempts:r.attempts+1,timestamp:e})}this.retryQueue.length>0&&setTimeout(()=>this.processRetryQueue(),this.config.retryDelay)}generateErrorId(){return"err_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}generateBatchId(){return"batch_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}getStats(){return{totalErrors:this.errorCount,queuedErrors:this.errorQueue.length,retryQueueSize:this.retryQueue.length,lastErrorTime:this.lastErrorTime,sessionId:this.sessionId}}clearQueue(){this.errorQueue=[],this.retryQueue=[]}destroy(){this.errorQueue.length>0&&this.sendBatch(),this.batchInterval&&clearInterval(this.batchInterval),this.clearQueue()}}class v{constructor(e){this.config=e,this.alertContainer=null,this.activeAlerts=new Map,this.alertCounter=0,this.createAlertContainer(),this.injectStyles()}createAlertContainer(){this.config.showUserAlerts&&(this.alertContainer=document.createElement("div"),this.alertContainer.id="rum-alert-container",this.alertContainer.className=`rum-alerts rum-alerts-${this.config.alertPosition}`,document.body.appendChild(this.alertContainer))}injectStyles(){if(!this.config.showUserAlerts)return;const e=document.createElement("style");e.textContent="\n      .rum-alerts {\n        position: fixed;\n        z-index: 10000;\n        pointer-events: none;\n        max-width: 400px;\n      }\n      \n      .rum-alerts-top-right {\n        top: 20px;\n        right: 20px;\n      }\n      \n      .rum-alerts-top-left {\n        top: 20px;\n        left: 20px;\n      }\n      \n      .rum-alerts-bottom-right {\n        bottom: 20px;\n        right: 20px;\n      }\n      \n      .rum-alerts-bottom-left {\n        bottom: 20px;\n        left: 20px;\n      }\n      \n      .rum-alert {\n        background: white;\n        border-radius: 8px;\n        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        margin-bottom: 12px;\n        padding: 16px;\n        pointer-events: auto;\n        transform: translateX(100%);\n        transition: all 0.3s ease;\n        border-left: 4px solid #ccc;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: 14px;\n        line-height: 1.4;\n      }\n      \n      .rum-alert.rum-alert-visible {\n        transform: translateX(0);\n      }\n      \n      .rum-alert-error {\n        border-left-color: #ef4444;\n      }\n      \n      .rum-alert-warning {\n        border-left-color: #f59e0b;\n      }\n      \n      .rum-alert-info {\n        border-left-color: #3b82f6;\n      }\n      \n      .rum-alert-success {\n        border-left-color: #10b981;\n      }\n      \n      .rum-alert-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        margin-bottom: 8px;\n      }\n      \n      .rum-alert-title {\n        font-weight: 600;\n        color: #1f2937;\n        margin: 0;\n      }\n      \n      .rum-alert-close {\n        background: none;\n        border: none;\n        font-size: 18px;\n        cursor: pointer;\n        color: #6b7280;\n        padding: 0;\n        width: 20px;\n        height: 20px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n      \n      .rum-alert-close:hover {\n        color: #374151;\n      }\n      \n      .rum-alert-message {\n        color: #4b5563;\n        margin-bottom: 12px;\n      }\n      \n      .rum-alert-actions {\n        display: flex;\n        gap: 8px;\n        flex-wrap: wrap;\n      }\n      \n      .rum-alert-button {\n        background: #f3f4f6;\n        border: 1px solid #d1d5db;\n        border-radius: 4px;\n        padding: 6px 12px;\n        font-size: 12px;\n        cursor: pointer;\n        transition: background-color 0.2s;\n      }\n      \n      .rum-alert-button:hover {\n        background: #e5e7eb;\n      }\n      \n      .rum-alert-button-primary {\n        background: #3b82f6;\n        color: white;\n        border-color: #3b82f6;\n      }\n      \n      .rum-alert-button-primary:hover {\n        background: #2563eb;\n      }\n    ",document.head.appendChild(e)}showErrorAlert(e){if(!this.config.showUserAlerts||!this.alertContainer)return;const t=this.getAlertTypeFromSeverity(e.severity),r=++this.alertCounter,n=this.createAlert({id:r,type:t,title:this.getErrorTitle(e),message:this.getErrorMessage(e),actions:this.getErrorActions(e)});return this.alertContainer.appendChild(n),this.activeAlerts.set(r,n),setTimeout(()=>{n.classList.add("rum-alert-visible")},10),e.severity!==s&&setTimeout(()=>{this.hideAlert(r)},this.config.alertDuration),r}createAlert({id:e,type:t,title:r,message:n,actions:o}){const s=document.createElement("div");s.className=`rum-alert rum-alert-${t}`,s.dataset.alertId=e;const i=document.createElement("div");i.className="rum-alert-header";const a=document.createElement("h4");a.className="rum-alert-title",a.textContent=r;const l=document.createElement("button");l.className="rum-alert-close",l.innerHTML="×",l.onclick=()=>this.hideAlert(e),i.appendChild(a),i.appendChild(l);const c=document.createElement("div");if(c.className="rum-alert-message",c.textContent=n,s.appendChild(i),s.appendChild(c),o&&o.length>0){const t=document.createElement("div");t.className="rum-alert-actions",o.forEach(r=>{const n=document.createElement("button");n.className="rum-alert-button "+(r.primary?"rum-alert-button-primary":""),n.textContent=r.text,n.onclick=()=>{r.handler(),!1!==r.closeOnClick&&this.hideAlert(e)},t.appendChild(n)}),s.appendChild(t)}return s}hideAlert(e){const t=this.activeAlerts.get(e);t&&(t.classList.remove("rum-alert-visible"),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),this.activeAlerts.delete(e)},300))}getAlertTypeFromSeverity(e){switch(e){case s:case o:return d;case n:return g;default:return p}}getErrorTitle(e){switch(e.severity){case s:return"Critical Error Detected";case o:return"Error Occurred";case n:return"Warning";case r:return"Notice";default:return"Error"}}getErrorMessage(e){switch(e.type){case"javascript":return"A JavaScript error occurred that may affect functionality.";case"network":return"A network request failed. Please check your connection.";case"resource":return"A resource failed to load properly.";default:return"An unexpected error occurred."}}getErrorActions(e){const t=[];return e.severity===s&&t.push({text:"Reload Page",primary:!0,handler:()=>window.location.reload()}),"network"===e.type&&t.push({text:"Retry",primary:!0,handler:()=>{window.dispatchEvent(new CustomEvent("rum:retry",{detail:{errorData:e}}))}}),t.push({text:"Contact Support",handler:()=>{window.dispatchEvent(new CustomEvent("rum:contact-support",{detail:{errorData:e}}))}}),t}showSuccess(e){if(!this.config.showUserAlerts||!this.alertContainer)return;const t=++this.alertCounter,r=this.createAlert({id:t,type:m,title:"Success",message:e,actions:[]});return this.alertContainer.appendChild(r),this.activeAlerts.set(t,r),setTimeout(()=>{r.classList.add("rum-alert-visible")},10),setTimeout(()=>{this.hideAlert(t)},3e3),t}clearAllAlerts(){this.activeAlerts.forEach((e,t)=>{this.hideAlert(t)})}destroy(){this.clearAllAlerts(),this.alertContainer&&this.alertContainer.parentNode&&this.alertContainer.parentNode.removeChild(this.alertContainer)}}class A{constructor(e={}){this.config=w(t,e),this.sessionId="rum_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),this.logger=null,this.errorCapture=null,this.ui=null,this.errorCounts=new Map,this.lastAlertTime=0,this.throttledAlert=function(e,t){let r;return function(){const n=arguments,o=this;r||(e.apply(o,n),r=!0,setTimeout(()=>r=!1,t))}}(e=>{this.handleCriticalError(e)},5e3),"loading"===document.readyState?document.addEventListener("DOMContentLoaded",()=>this.initialize()):this.initialize()}initialize(){try{this.config.debug&&console.log("RUM: Initializing with config",this.config),this.logger=new C(this.config,this.sessionId),this.ui=new v(this.config),this.errorCapture=new E(this.config,{logError:e=>this.handleError(e)}),this.setupPageVisibilityHandler(),this.setupBeforeUnloadHandler(),this.setupCustomEventListeners(),this.config.debug&&console.log("RUM: Initialized successfully"),this.exposeGlobalMethods()}catch(e){console.error("RUM: Failed to initialize",e)}}handleError(e){this.logger.logError(e),this.trackErrorFrequency(e),e.severity!==s&&e.severity!==o||this.ui.showErrorAlert(e),this.shouldTriggerAlert()&&this.throttledAlert(e)}trackErrorFrequency(e){const t=Date.now(),r=this.config.timeWindow;for(const[e,n]of this.errorCounts.entries())t-e>r&&this.errorCounts.delete(e);const n=Math.floor(t/r)*r,o=this.errorCounts.get(n)||0;this.errorCounts.set(n,o+1)}shouldTriggerAlert(){return Array.from(this.errorCounts.values()).reduce((e,t)=>e+t,0)>=this.config.errorThreshold}handleCriticalError(e){const t=Date.now();t-this.lastAlertTime<6e4||(this.lastAlertTime=t,window.dispatchEvent(new CustomEvent("rum:critical-error",{detail:{errorData:e,sessionId:this.sessionId,errorCount:Array.from(this.errorCounts.values()).reduce((e,t)=>e+t,0)}})),this.config.debug&&console.warn("RUM: Critical error threshold exceeded",e))}setupPageVisibilityHandler(){document.addEventListener("visibilitychange",()=>{document.hidden&&this.logger.sendBatch()})}setupBeforeUnloadHandler(){window.addEventListener("beforeunload",()=>{this.logger.sendBatch()})}setupCustomEventListeners(){window.addEventListener("rum:retry",e=>{const{errorData:t}=e.detail;this.config.debug&&console.log("RUM: Retry requested for error",t),setTimeout(()=>{this.ui.showSuccess("Retry completed successfully")},1e3)}),window.addEventListener("rum:contact-support",e=>{const{errorData:t}=e.detail;this.config.debug&&console.log("RUM: Support contact requested for error",t);const r=`mailto:<EMAIL>?subject=Error Report&body=Error ID: ${t.errorId}%0ASession ID: ${this.sessionId}%0AError: ${encodeURIComponent(t.message)}`;window.open(r)})}exposeGlobalMethods(){window.RUM={logError:(e,t={})=>{this.errorCapture.logCustomError(e,t)},getStats:()=>({...this.logger.getStats(),config:this.config,errorFrequency:Object.fromEntries(this.errorCounts)}),updateConfig:e=>{this.config=w(this.config,e),this.config.debug&&console.log("RUM: Configuration updated",this.config)},clearErrors:()=>{this.logger.clearQueue(),this.errorCounts.clear(),this.ui.clearAllAlerts()},destroy:()=>{this.destroy()}}}getStats(){return{sessionId:this.sessionId,config:this.config,logger:this.logger?this.logger.getStats():null,errorFrequency:Object.fromEntries(this.errorCounts),lastAlertTime:this.lastAlertTime}}updateConfig(e){this.config=w(this.config,e),this.config.debug&&console.log("RUM: Configuration updated",this.config)}destroy(){this.config.debug&&console.log("RUM: Destroying instance"),this.errorCapture&&this.errorCapture.destroy(),this.logger&&this.logger.destroy(),this.ui&&this.ui.destroy(),this.errorCounts.clear(),window.RUM&&delete window.RUM}}document.addEventListener("DOMContentLoaded",()=>{const e=document.querySelector("script[data-rum-config]");if(e)try{const t=JSON.parse(e.dataset.rumConfig);new A(t)}catch(e){console.error("RUM: Failed to parse config from script tag",e)}}),e.RUMScript=A,e.default=A,Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=rum-script.min.js.map
