'use client';

import { useState, useEffect } from 'react';

declare global {
  interface Window {
    RUM?: {
      logError: (message: string, details?: any) => void;
      getStats: () => any;
      updateConfig: (config: any) => void;
      clearErrors: () => void;
    };
  }
}

export default function Home() {
  const [rumStats, setRumStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Update RUM stats periodically
  useEffect(() => {
    const updateStats = () => {
      if (window.RUM) {
        setRumStats(window.RUM.getStats());
      }
    };

    updateStats();
    const interval = setInterval(updateStats, 5000);
    return () => clearInterval(interval);
  }, []);

  const triggerJavaScriptError = () => {
    setIsLoading(true);
    setTimeout(() => {
      // This will trigger a JavaScript error
      (window as any).nonExistentFunction();
      setIsLoading(false);
    }, 1000);
  };

  const triggerNetworkError = async () => {
    setIsLoading(true);
    try {
      // This will trigger a network error
      await fetch('/api/non-existent-endpoint');
    } catch (error) {
      console.log('Network error triggered');
    }
    setIsLoading(false);
  };

  const triggerConsoleError = () => {
    console.error('This is a test console error from the demo app');
  };

  const triggerCustomError = () => {
    if (window.RUM) {
      window.RUM.logError('Custom error triggered by user', {
        severity: 'high',
        component: 'demo-page',
        action: 'button-click',
        metadata: {
          timestamp: new Date().toISOString(),
          userAction: 'trigger-custom-error'
        }
      });
    }
  };

  const clearAllErrors = () => {
    if (window.RUM) {
      window.RUM.clearErrors();
      setRumStats(window.RUM.getStats());
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <header className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🚨 RUM Alerting System Demo
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Real User Monitoring with Error Logging and Alerting.
            This demo showcases error capture, logging, and user recovery features.
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Error Testing Panel */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
              🧪 Error Testing Panel
            </h2>

            <div className="space-y-4">
              <button
                onClick={triggerJavaScriptError}
                disabled={isLoading}
                className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                {isLoading ? 'Triggering...' : 'Trigger JavaScript Error'}
              </button>

              <button
                onClick={triggerNetworkError}
                disabled={isLoading}
                className="w-full bg-orange-500 hover:bg-orange-600 disabled:bg-orange-300 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                {isLoading ? 'Triggering...' : 'Trigger Network Error'}
              </button>

              <button
                onClick={triggerConsoleError}
                className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                Trigger Console Error
              </button>

              <button
                onClick={triggerCustomError}
                className="w-full bg-purple-500 hover:bg-purple-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                Trigger Custom Error
              </button>

              <button
                onClick={clearAllErrors}
                className="w-full bg-gray-500 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                Clear All Errors
              </button>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">💡 What happens when you trigger errors:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Errors are captured automatically by the RUM script</li>
                <li>• Error details are sent to the server asynchronously</li>
                <li>• User alerts appear for critical/high severity errors</li>
                <li>• Admin notifications are sent when thresholds are exceeded</li>
              </ul>
            </div>
          </div>

          {/* RUM Statistics Panel */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
              📊 RUM Statistics
            </h2>

            {rumStats ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {rumStats.logger?.totalErrors || 0}
                    </div>
                    <div className="text-sm text-blue-800">Total Errors</div>
                  </div>

                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {rumStats.logger?.queuedErrors || 0}
                    </div>
                    <div className="text-sm text-green-800">Queued Errors</div>
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-semibold text-gray-800 mb-2">Session Info</h3>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div><strong>Session ID:</strong> {rumStats.sessionId}</div>
                    <div><strong>Environment:</strong> {rumStats.config?.environment}</div>
                    <div><strong>Logging Enabled:</strong> {rumStats.config?.enableLogging ? 'Yes' : 'No'}</div>
                  </div>
                </div>

                {Object.keys(rumStats.errorFrequency || {}).length > 0 && (
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-yellow-800 mb-2">Error Frequency</h3>
                    <div className="text-sm text-yellow-700">
                      {Object.entries(rumStats.errorFrequency).map(([time, count]) => (
                        <div key={time}>
                          {new Date(parseInt(time)).toLocaleTimeString()}: {count as number} errors
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                Loading RUM statistics...
              </div>
            )}
          </div>
        </div>

        {/* Features Overview */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
            🚀 RUM System Features
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-2">🔍</div>
              <h3 className="font-semibold text-gray-800 mb-2">Error Capture</h3>
              <p className="text-sm text-gray-600">
                Automatically captures JavaScript, network, console, and resource errors
              </p>
            </div>

            <div className="text-center">
              <div className="text-3xl mb-2">📡</div>
              <h3 className="font-semibold text-gray-800 mb-2">Async Logging</h3>
              <p className="text-sm text-gray-600">
                Sends error data to server without blocking the main thread
              </p>
            </div>

            <div className="text-center">
              <div className="text-3xl mb-2">🚨</div>
              <h3 className="font-semibold text-gray-800 mb-2">Smart Alerts</h3>
              <p className="text-sm text-gray-600">
                Triggers email/SMS alerts when error thresholds are exceeded
              </p>
            </div>

            <div className="text-center">
              <div className="text-3xl mb-2">🔧</div>
              <h3 className="font-semibold text-gray-800 mb-2">User Recovery</h3>
              <p className="text-sm text-gray-600">
                Provides retry, reload, and support contact options for users
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-12 text-center text-gray-600">
          <p>
            RUM Alerting System Demo - Built with Next.js, Express.js, and MongoDB
          </p>
          <p className="text-sm mt-2">
            Check the browser console and network tab to see the RUM script in action!
          </p>
        </footer>
      </div>
    </div>
  );
}
