import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Script from "next/script";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "RUM Alerting Demo - Real User Monitoring",
  description: "Demo application showcasing RUM script with error logging and alerting",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}

        {/* RUM Script Integration */}
        <Script
          src="/rum-script.min.js"
          strategy="afterInteractive"
          onLoad={() => {
            // Initialize RUM with configuration
            if (typeof window !== 'undefined' && (window as any).RUMScript) {
              new (window as any).RUMScript({
                baseUrl: 'http://localhost:3001',
                logEndpoint: '/api/errors',
                environment: process.env.NODE_ENV || 'development',
                enableLogging: true,
                enableConsoleCapture: true,
                enableNetworkCapture: true,
                enableResourceCapture: true,
                showUserAlerts: true,
                alertPosition: 'top-right',
                debug: process.env.NODE_ENV === 'development',
                errorThreshold: 3,
                timeWindow: 2 * 60 * 1000, // 2 minutes
                apiKey: process.env.NODE_ENV === 'production' ? 'your-api-key' : null
              });

              console.log('RUM Script initialized successfully');
            }
          }}
        />
      </body>
    </html>
  );
}
